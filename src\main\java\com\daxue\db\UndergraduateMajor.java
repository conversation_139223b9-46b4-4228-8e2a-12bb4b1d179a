package com.daxue.db;

import java.util.List;
import java.util.ArrayList;

/**
 * 本科专业实体类
 * 对应本科专业目录数据结构
 */
public class UndergraduateMajor {
    
    /** 学科门类代码 (如: 01, 02, 03...) */
    private String categoryOneCode;
    
    /** 专业类代码 (如: 0101, 0201...) */
    private String categoryTwoCode;
    
    /** 学科门类名称 (如: 哲学, 经济学, 法学...) */
    private String categoryOne;
    
    /** 专业类名称 (如: 哲学类, 经济学类...) */
    private String categoryTwo;
    
    /** 专业类型 (NULL:基本专业, T:特设专业, K:国家控制布点专业, TK:特设+控制) */
    private String type;
    
    /** 专业名称 */
    private String majorName;
    
    /** 专业代码 */
    private String majorCode;
    
    public UndergraduateMajor() {}
    
    public UndergraduateMajor(String categoryOneCode, String categoryTwoCode, String categoryOne, 
                             String categoryTwo, String type, String majorName, String majorCode) {
        this.categoryOneCode = categoryOneCode;
        this.categoryTwoCode = categoryTwoCode;
        this.categoryOne = categoryOne;
        this.categoryTwo = categoryTwo;
        this.type = type;
        this.majorName = majorName;
        this.majorCode = majorCode;
    }
    
    // Getters and Setters
    public String getCategoryOneCode() {
        return categoryOneCode;
    }
    
    public void setCategoryOneCode(String categoryOneCode) {
        this.categoryOneCode = categoryOneCode;
    }
    
    public String getCategoryTwoCode() {
        return categoryTwoCode;
    }
    
    public void setCategoryTwoCode(String categoryTwoCode) {
        this.categoryTwoCode = categoryTwoCode;
    }
    
    public String getCategoryOne() {
        return categoryOne;
    }
    
    public void setCategoryOne(String categoryOne) {
        this.categoryOne = categoryOne;
    }
    
    public String getCategoryTwo() {
        return categoryTwo;
    }
    
    public void setCategoryTwo(String categoryTwo) {
        this.categoryTwo = categoryTwo;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getMajorName() {
        return majorName;
    }
    
    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }
    
    public String getMajorCode() {
        return majorCode;
    }
    
    public void setMajorCode(String majorCode) {
        this.majorCode = majorCode;
    }
    
    /**
     * 获取专业名称的核心关键词
     * 去除常见的修饰词，提取核心专业词汇
     */
    public String getCoreKeywords() {
        if (majorName == null) return "";
        
        // 使用更智能的关键词提取方法
        List<String> keywords = extractKeywordsFromMajorName(majorName);
        return String.join(",", keywords);
    }
    
    /**
     * 从专业名称中提取关键词
     * 保留有意义的词汇，去除修饰词
     */
    private List<String> extractKeywordsFromMajorName(String name) {
        List<String> keywords = new ArrayList<>();
        
        // 先进行基本的词汇分割和清理
        String cleanName = name.replaceAll("[()（）\\[\\]【】<>《》、，。；：！？\\s]+", "");
        
        // 定义修饰词列表
        String[] modifiers = {"应用", "理论", "基础", "现代", "智能", "新", "高级", "国际", "专业"};
        
        // 定义核心学科词汇模式
        String[] patterns = {
            "计算机.*?(?=科学|技术|工程|$)",
            "电子.*?(?=信息|科学|技术|工程|$)", 
            "机械.*?(?=设计|制造|工程|$)",
            "材料.*?(?=科学|工程|$)",
            "化学.*?(?=工程|工艺|$)",
            "土木.*?(?=工程|$)",
            "软件.*?(?=工程|$)",
            "网络.*?(?=工程|$)",
            "信息.*?(?=安全|管理|工程|系统|$)",
            "数据.*?(?=科学|技术|$)",
            "人工智能",
            "大数据",
            "物联网",
            "区块链"
        };
        
        // 使用正则表达式提取核心词汇
        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(cleanName);
            while (m.find()) {
                String match = m.group();
                if (!match.isEmpty() && match.length() > 1) {
                    keywords.add(match);
                }
            }
        }
        
        // 如果没有匹配到模式，使用传统方法
        if (keywords.isEmpty()) {
            String core = cleanName;
            // 去除修饰词
            for (String modifier : modifiers) {
                core = core.replace(modifier, "");
            }
            
            // 去除常见后缀，但保留有意义的部分
            core = core.replace("及其自动化", "")
                      .replace("与", "")
                      .replace("及", "");
            
            // 分割成有意义的词汇片段
            if (core.length() > 0) {
                // 简单的中文词汇分割
                List<String> segments = segmentChineseWords(core);
                keywords.addAll(segments);
            }
        }
        
        // 过滤掉过短的词汇
        keywords.removeIf(keyword -> keyword.length() < 2);
        
        return keywords;
    }
    
    /**
     * 简单的中文词汇分割
     * 基于常见的学科词汇进行分割
     */
    private List<String> segmentChineseWords(String text) {
        List<String> segments = new ArrayList<>();
        
        // 定义常见的学科关键词
        String[] commonWords = {
            "哲学", "经济", "法学", "教育", "文学", "历史", "理学", "工学", "农学", "医学", "管理", "艺术",
            "数学", "物理", "化学", "生物", "地理", "天文", "地质", "心理", "统计",
            "机械", "电气", "电子", "计算机", "软件", "网络", "通信", "信息", "自动化",
            "材料", "能源", "动力", "土木", "建筑", "环境", "化工", "制药", "食品",
            "交通", "运输", "航空", "航天", "船舶", "海洋", "核", "安全",
            "金融", "会计", "营销", "人力", "物流", "电商", "旅游",
            "临床", "基础", "预防", "中医", "药学", "护理", "口腔"
        };
        
        // 贪心匹配最长词汇
        int i = 0;
        while (i < text.length()) {
            boolean found = false;
            // 从最长可能的词开始匹配
            for (int len = Math.min(4, text.length() - i); len >= 2; len--) {
                String candidate = text.substring(i, i + len);
                for (String word : commonWords) {
                    if (candidate.equals(word)) {
                        segments.add(word);
                        i += len;
                        found = true;
                        break;
                    }
                }
                if (found) break;
            }
            
            if (!found) {
                // 如果没有匹配到，取单个字符（如果是有意义的）
                String singleChar = text.substring(i, i + 1);
                if (!isModifierChar(singleChar)) {
                    segments.add(singleChar);
                }
                i++;
            }
        }
        
        return segments;
    }
    
    /**
     * 判断是否为修饰性字符
     */
    private boolean isModifierChar(String ch) {
        return ch.matches("[与及和或的等]");
    }
    
    @Override
    public String toString() {
        return "UndergraduateMajor{" +
                "categoryOneCode='" + categoryOneCode + '\'' +
                ", categoryTwoCode='" + categoryTwoCode + '\'' +
                ", categoryOne='" + categoryOne + '\'' +
                ", categoryTwo='" + categoryTwo + '\'' +
                ", type='" + type + '\'' +
                ", majorName='" + majorName + '\'' +
                ", majorCode='" + majorCode + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        UndergraduateMajor that = (UndergraduateMajor) obj;
        return majorCode != null ? majorCode.equals(that.majorCode) : that.majorCode == null;
    }
    
    @Override
    public int hashCode() {
        return majorCode != null ? majorCode.hashCode() : 0;
    }
}