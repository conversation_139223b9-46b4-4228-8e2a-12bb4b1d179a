package com.daxue.utils;

import com.daxue.db.UndergraduateMajor;
import com.daxue.db.GraduateMajor;
import java.util.List;
import java.util.ArrayList;

/**
 * 专业数据仓库类 - 硬编码所有专业数据
 * 包含858个本科专业和791个研究生专业的完整数据
 */
public class MajorDataRepository {

    private static List<UndergraduateMajor> undergraduateMajors = new ArrayList<>();
    private static List<GraduateMajor> graduateMajors = new ArrayList<>();

    static {
        initializeUndergraduateMajors();
        initializeGraduateMajors();
    }

    /**
     * 获取所有本科专业
     */
    public static List<UndergraduateMajor> getUndergraduateMajors() {
        return new ArrayList<>(undergraduateMajors);
    }

    /**
     * 获取所有研究生专业
     */
    public static List<GraduateMajor> getGraduateMajors() {
        return new ArrayList<>(graduateMajors);
    }

    /**
     * 创建配置好的专业匹配器
     * 注意：需要您自行实现 MajorMatcher 类
     */
    public static MajorMatcher createMajorMatcher() {
        return new MajorMatcher(getUndergraduateMajors(), getGraduateMajors());
    }

    /**
     * 添加本科专业的辅助方法
     * 注意：需要您自行实现 UndergraduateMajor 实体类
     */
    private static void addUndergraduate(String categoryOneCode, String categoryTwoCode,
                                       String categoryOne, String categoryTwo,
                                       String type, String majorName, String majorCode) {
        UndergraduateMajor major = new UndergraduateMajor();
        major.setCategoryOneCode(categoryOneCode);
        major.setCategoryTwoCode(categoryTwoCode);
        major.setCategoryOne(categoryOne);
        major.setCategoryTwo(categoryTwo);
        major.setType(type);
        major.setMajorName(majorName);
        major.setMajorCode(majorCode);
        undergraduateMajors.add(major);
    }

    /**
     * 添加研究生专业的辅助方法
     * 注意：需要您自行实现 GraduateMajor 实体类
     */
    private static void addGraduate(String categoryOneCode, String categoryTwoCode,
                                  String categoryOne, String categoryTwo,
                                  String type, String majorName, String majorCode, int sort) {
        GraduateMajor major = new GraduateMajor();
        major.setCategoryOneCode(categoryOneCode);
        major.setCategoryTwoCode(categoryTwoCode);
        major.setCategoryOne(categoryOne);
        major.setCategoryTwo(categoryTwo);
        major.setType(type);
        major.setMajorName(majorName);
        major.setMajorCode(majorCode);
        major.setSort(sort);
        graduateMajors.add(major);
    }

    /**
     * 初始化本科专业数据
     */
    private static void initializeUndergraduateMajors() {
        addUndergraduate("01","0101","哲学","哲学类","本科","哲学","010101");
        addUndergraduate("01","0101","哲学","哲学类","本科","逻辑学","010102");
        addUndergraduate("01","0101","哲学","哲学类","本科","宗教学","010103K");
        addUndergraduate("01","0101","哲学","哲学类","本科","伦理学","010104T");
        addUndergraduate("02","0201","经济学","经济学类","本科","经济学","020101");
        addUndergraduate("02","0201","经济学","经济学类","本科","经济统计学","020102");
        addUndergraduate("02","0201","经济学","经济学类","本科","国民经济管理","020103T");
        addUndergraduate("02","0201","经济学","经济学类","本科","资源与环境经济学","020104T");
        addUndergraduate("02","0201","经济学","经济学类","本科","商务经济学","020105T");
        addUndergraduate("02","0201","经济学","经济学类","本科","能源经济","020106T");
        addUndergraduate("02","0201","经济学","经济学类","本科","劳动经济学","020107T");
        addUndergraduate("02","0201","经济学","经济学类","本科","经济工程","020108T");
        addUndergraduate("02","0201","经济学","经济学类","本科","数字经济","020109T");
        addUndergraduate("02","0202","经济学","财政学类","本科","财政学","020201K");
        addUndergraduate("02","0202","经济学","财政学类","本科","税收学","020202");
        addUndergraduate("02","0203","经济学","金融学类","本科","金融学","020301K");
        addUndergraduate("02","0203","经济学","金融学类","本科","金融工程","020302");
        addUndergraduate("02","0203","经济学","金融学类","本科","保险学","020303");
        addUndergraduate("02","0203","经济学","金融学类","本科","投资学","020304");
        addUndergraduate("02","0203","经济学","金融学类","本科","金融数学","020305T");
        addUndergraduate("02","0203","经济学","金融学类","本科","信用管理","020306T");
        addUndergraduate("02","0203","经济学","金融学类","本科","经济与金融","020307T");
        addUndergraduate("02","0203","经济学","金融学类","本科","精算学","020308T");
        addUndergraduate("02","0203","经济学","金融学类","本科","互联网金融","020309T");
        addUndergraduate("02","0203","经济学","金融学类","本科","金融科技","020310T");
        addUndergraduate("02","0204","经济学","经济与贸易类","本科","国际经济与贸易","020401");
        addUndergraduate("02","0204","经济学","经济与贸易类","本科","贸易经济","020402");
        addUndergraduate("03","0301","法学","法学类","本科","法学","030101K");
        addUndergraduate("03","0301","法学","法学类","本科","知识产权","030102T");
        addUndergraduate("03","0301","法学","法学类","本科","监狱学","030103T");
        addUndergraduate("03","0301","法学","法学类","本科","信用风险管理与法律防控","030104T");
        addUndergraduate("03","0301","法学","法学类","本科","国际经贸规则","030105T");
        addUndergraduate("03","0301","法学","法学类","本科","司法警察学","030106TK");
        addUndergraduate("03","0301","法学","法学类","本科","社区矫正","030107TK");
        addUndergraduate("03","0302","法学","政治学类","本科","政治学与行政学","030201");
        addUndergraduate("03","0302","法学","政治学类","本科","国际政治","030202");
        addUndergraduate("03","0302","法学","政治学类","本科","外交学","030203");
        addUndergraduate("03","0302","法学","政治学类","本科","国际事务与国际关系","030204T");
        addUndergraduate("03","0302","法学","政治学类","本科","政治学、经济学与哲学","030205T");
        addUndergraduate("03","0302","法学","政治学类","本科","国际组织与全球治理","030206T");
        addUndergraduate("03","0303","法学","社会学类","本科","社会学","030301");
        addUndergraduate("03","0303","法学","社会学类","本科","社会工作","030302");
        addUndergraduate("03","0303","法学","社会学类","本科","人类学","030303T");
        addUndergraduate("03","0303","法学","社会学类","本科","女性学","030304T");
        addUndergraduate("03","0303","法学","社会学类","本科","家政学","030305T");
        addUndergraduate("03","0303","法学","社会学类","本科","老年学","030306T");
        addUndergraduate("03","0303","法学","社会学类","本科","社会政策","030307T");
        addUndergraduate("03","0304","法学","民族学类","本科","民族学","030401");
        addUndergraduate("03","0305","法学","马克思主义理论类","本科","科学社会主义","030501");
        addUndergraduate("03","0305","法学","马克思主义理论类","本科","中国共产党历史","030502");
        addUndergraduate("03","0305","法学","马克思主义理论类","本科","思想政治教育","030503");
        addUndergraduate("03","0305","法学","马克思主义理论类","本科","马克思主义理论","030504T");
        addUndergraduate("03","0306","法学","公安学类","本科","治安学","030601K");
        addUndergraduate("03","0306","法学","公安学类","本科","侦查学","030602K");
        addUndergraduate("03","0306","法学","公安学类","本科","边防管理","030603K");
        addUndergraduate("03","0306","法学","公安学类","本科","禁毒学","030604TK");
        addUndergraduate("03","0306","法学","公安学类","本科","警犬技术","030605TK");
        addUndergraduate("03","0306","法学","公安学类","本科","经济犯罪侦查","030606TK");
        addUndergraduate("03","0306","法学","公安学类","本科","边防指挥","030607TK");
        addUndergraduate("03","0306","法学","公安学类","本科","消防指挥","030608TK");
        addUndergraduate("03","0306","法学","公安学类","本科","警卫学","030609TK");
        addUndergraduate("03","0306","法学","公安学类","本科","公安情报学","030610TK");
        addUndergraduate("03","0306","法学","公安学类","本科","犯罪学","030611TK");
        addUndergraduate("03","0306","法学","公安学类","本科","公安管理学","030612TK");
        addUndergraduate("03","0306","法学","公安学类","本科","涉外警务","030613TK");
        addUndergraduate("03","0306","法学","公安学类","本科","国内安全保卫","030614TK");
        addUndergraduate("03","0306","法学","公安学类","本科","警务指挥与战术","030615TK");
        addUndergraduate("03","0306","法学","公安学类","本科","技术侦查学","030616TK");
        addUndergraduate("03","0306","法学","公安学类","本科","海警执法","030617TK");
        addUndergraduate("03","0306","法学","公安学类","本科","公安政治工作","030618TK");
        addUndergraduate("03","0306","法学","公安学类","本科","移民管理","030619TK");
        addUndergraduate("04","0401","教育学","教育学类","本科","教育学","040101");
        addUndergraduate("04","0401","教育学","教育学类","本科","科学教育","040102");
        addUndergraduate("04","0401","教育学","教育学类","本科","人文教育","040103");
        addUndergraduate("04","0401","教育学","教育学类","本科","教育技术学","040104");
        addUndergraduate("04","0401","教育学","教育学类","本科","艺术教育","040105");
        addUndergraduate("04","0401","教育学","教育学类","本科","学前教育","040106");
        addUndergraduate("04","0401","教育学","教育学类","本科","小学教育","040107");
        addUndergraduate("04","0401","教育学","教育学类","本科","特殊教育","040108");
        addUndergraduate("04","0401","教育学","教育学类","本科","华文教育","040109T");
        addUndergraduate("04","0401","教育学","教育学类","本科","教育康复学","040110T");
        addUndergraduate("04","0401","教育学","教育学类","本科","卫生教育","040111T");
        addUndergraduate("04","0401","教育学","教育学类","本科","认知科学与技术","040112T");
        addUndergraduate("04","0401","教育学","教育学类","本科","融合教育","040113T");
        addUndergraduate("04","0402","教育学","体育学类","本科","体育教育","040201");
        addUndergraduate("04","0402","教育学","体育学类","本科","运动训练","040202K");
        addUndergraduate("04","0402","教育学","体育学类","本科","社会体育指导与管理","040203");
        addUndergraduate("04","0402","教育学","体育学类","本科","武术与民族传统体育","040204K");
        addUndergraduate("04","0402","教育学","体育学类","本科","运动人体科学","040205");
        addUndergraduate("04","0402","教育学","体育学类","本科","运动康复","040206T");
        addUndergraduate("04","0402","教育学","体育学类","本科","休闲体育","040207T");
        addUndergraduate("04","0402","教育学","体育学类","本科","体能训练","040208T");
        addUndergraduate("04","0402","教育学","体育学类","本科","冰雪运动","040209T");
        addUndergraduate("04","0402","教育学","体育学类","本科","电子竞技运动与管理","040210T");
        addUndergraduate("04","0402","教育学","体育学类","本科","智能体育工程","040211T");
        addUndergraduate("04","0402","教育学","体育学类","本科","体育旅游","040212T");
        addUndergraduate("04","0402","教育学","体育学类","本科","运动能力开发","040213T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","汉语言文学","050101");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","汉语言","050102");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","汉语国际教育","050103");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","中国少数民族语言文学","050104");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","古典文献学","050105T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","应用语言学","050106T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","秘书学","050107T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","中国语言与文化","050108T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","手语翻译","050109T");
        addUndergraduate("05","0501","文学","中国语言文学类","本科","桑戈语","050110T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","英语","050201");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","俄语","050202");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","德语","050203");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","法语","050204");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","西班牙语","050205");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","阿拉伯语","050206");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","日语","050207");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","波斯语","050208");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","朝鲜语","050209");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","菲律宾语","050210T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","梵语巴利语","050211T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","印度尼西亚语","050212T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","印地语","050213T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","柬埔寨语","050214T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","老挝语","050215T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","缅甸语","050216T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","马来语","050217T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","蒙古语","050218T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","僧伽罗语","050219T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","泰语","050220T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","乌尔都语","050221T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","希伯来语","050222T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","越南语","050223T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","豪萨语","050224T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","斯瓦希里语","050225T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","阿尔巴尼亚语","050226T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","保加利亚语","050227T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","波兰语","050228T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","捷克语","050229T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","斯洛伐克语","050230T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","罗马尼亚语","050231T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","葡萄牙语","050232T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","瑞典语","050233T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","塞尔维亚语","050234T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","土耳其语","050235T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","希腊语","050236T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","匈牙利语","050237T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","意大利语","050238T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","罗马尼亚语","050239T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","荷兰语","050240T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","芬兰语","050241T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","乌克兰语","050242T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","挪威语","050243T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","丹麦语","050244T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","冰岛语","050245T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","爱尔兰语","050246T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","拉脱维亚语","050247T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","立陶宛语","050248T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","斯洛文尼亚语","050249T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","爱沙尼亚语","050250T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","马耳他语","050251T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","哈萨克语","050252T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","乌兹别克语","050253T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","祖鲁语","050254T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","拉丁语","050255T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","世界语","050256T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","孟加拉语","050257T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","普什图语","050258T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","塔吉克语","050259T");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","翻译","050261");
        addUndergraduate("05","0502","文学","外国语言文学类","本科","商务英语","050262");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","新闻学","050301");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","广播电视学","050302");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","广告学","050303");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","传播学","050304");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","编辑出版学","050305");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","网络与新媒体","050306T");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","数字出版","050307T");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","时尚传播","050308T");
        addUndergraduate("05","0503","文学","新闻传播学类","本科","国际新闻与传播","050309T");
        addUndergraduate("06","0601","历史学","历史学类","本科","历史学","060101");
        addUndergraduate("06","0601","历史学","历史学类","本科","世界史","060102");
        addUndergraduate("06","0601","历史学","历史学类","本科","考古学","060103");
        addUndergraduate("06","0601","历史学","历史学类","本科","文物与博物馆学","060104T");
        addUndergraduate("06","0601","历史学","历史学类","本科","文物保护技术","060105T");
        addUndergraduate("06","0601","历史学","历史学类","本科","外国语言与外国历史","060106T");
        addUndergraduate("06","0601","历史学","历史学类","本科","文化遗产","060107T");
        addUndergraduate("06","0601","历史学","历史学类","本科","古文字学","060108T");
        addUndergraduate("07","0701","理学","数学类","本科","数学与应用数学","070101");
        addUndergraduate("07","0701","理学","数学类","本科","信息与计算科学","070102");
        addUndergraduate("07","0701","理学","数学类","本科","数理基础科学","070103T");
        addUndergraduate("07","0701","理学","数学类","本科","数据计算及应用","070104T");
        addUndergraduate("07","0702","理学","物理学类","本科","物理学","070201");
        addUndergraduate("07","0702","理学","物理学类","本科","应用物理学","070202");
        addUndergraduate("07","0702","理学","物理学类","本科","核物理","070203");
        addUndergraduate("07","0702","理学","物理学类","本科","声学","070204T");
        addUndergraduate("07","0702","理学","物理学类","本科","量子信息科学","070205T");
        addUndergraduate("07","0703","理学","化学类","本科","化学","070301");
        addUndergraduate("07","0703","理学","化学类","本科","应用化学","070302");
        addUndergraduate("07","0703","理学","化学类","本科","化学生物学","070303T");
        addUndergraduate("07","0703","理学","化学类","本科","分子科学与工程","070304T");
        addUndergraduate("07","0703","理学","化学类","本科","能源化学","070305T");
        addUndergraduate("07","0704","理学","天文学类","本科","天文学","070401");
        addUndergraduate("07","0705","理学","地理科学类","本科","地理科学","070501");
        addUndergraduate("07","0705","理学","地理科学类","本科","自然地理与资源环境","070502");
        addUndergraduate("07","0705","理学","地理科学类","本科","人文地理与城乡规划","070503");
        addUndergraduate("07","0705","理学","地理科学类","本科","地理信息科学","070504");
        addUndergraduate("07","0706","理学","大气科学类","本科","大气科学","070601");
        addUndergraduate("07","0706","理学","大气科学类","本科","应用气象学","070602");
        addUndergraduate("07","0707","理学","海洋科学类","本科","海洋科学","070701");
        addUndergraduate("07","0707","理学","海洋科学类","本科","海洋技术","070702");
        addUndergraduate("07","0707","理学","海洋科学类","本科","海洋资源与环境","070703T");
        addUndergraduate("07","0707","理学","海洋科学类","本科","军事海洋学","070704T");
        addUndergraduate("07","0708","理学","地球物理学类","本科","地球物理学","070801");
        addUndergraduate("07","0708","理学","地球物理学类","本科","空间科学与技术","070802");
        addUndergraduate("07","0708","理学","地球物理学类","本科","地球与空间科学","070803T");
        addUndergraduate("07","0709","理学","地质学类","本科","地质学","070901");
        addUndergraduate("07","0709","理学","地质学类","本科","地球化学","070902");
        addUndergraduate("07","0709","理学","地质学类","本科","地球信息科学与技术","070903T");
        addUndergraduate("07","0709","理学","地质学类","本科","古生物学","070904T");
        addUndergraduate("07","0710","理学","生物科学类","本科","生物科学","071001");
        addUndergraduate("07","0710","理学","生物科学类","本科","生物技术","071002");
        addUndergraduate("07","0710","理学","生物科学类","本科","生物信息学","071003T");
        addUndergraduate("07","0710","理学","生物科学类","本科","生态学","071004T");
        addUndergraduate("07","0710","理学","生物科学类","本科","整合科学","071005T");
        addUndergraduate("07","0710","理学","生物科学类","本科","神经科学","071006T");
        addUndergraduate("07","0711","理学","心理学类","本科","心理学","071101");
        addUndergraduate("07","0711","理学","心理学类","本科","应用心理学","071102");
        addUndergraduate("07","0712","理学","统计学类","本科","统计学","071201");
        addUndergraduate("07","0712","理学","统计学类","本科","应用统计学","071202T");
        addUndergraduate("08","0801","工学","力学类","本科","理论与应用力学","080101");
        addUndergraduate("08","0801","工学","力学类","本科","工程力学","080102");
        addUndergraduate("08","0802","工学","机械类","本科","机械工程","080201");
        addUndergraduate("08","0802","工学","机械类","本科","机械设计制造及其自动化","080202");
        addUndergraduate("08","0802","工学","机械类","本科","材料成型及控制工程","080203");
        addUndergraduate("08","0802","工学","机械类","本科","机械电子工程","080204");
        addUndergraduate("08","0802","工学","机械类","本科","工业设计","080205");
        addUndergraduate("08","0802","工学","机械类","本科","过程装备与控制工程","080206");
        addUndergraduate("08","0802","工学","机械类","本科","车辆工程","080207");
        addUndergraduate("08","0802","工学","机械类","本科","汽车服务工程","080208T");
        addUndergraduate("08","0802","工学","机械类","本科","机械工艺技术","080209T");
        addUndergraduate("08","0802","工学","机械类","本科","微机电系统工程","080210T");
        addUndergraduate("08","0802","工学","机械类","本科","机电技术教育","080211T");
        addUndergraduate("08","0802","工学","机械类","本科","汽车维修工程教育","080212T");
        addUndergraduate("08","0802","工学","机械类","本科","智能制造工程","080213T");
        addUndergraduate("08","0802","工学","机械类","本科","智能车辆工程","080214T");
        addUndergraduate("08","0802","工学","机械类","本科","仿生科学与工程","080215T");
        addUndergraduate("08","0802","工学","机械类","本科","新能源汽车工程","080216T");
        addUndergraduate("08","0803","工学","仪器类","本科","测控技术与仪器","080301");
        addUndergraduate("08","0803","工学","仪器类","本科","精密仪器","080302T");
        addUndergraduate("08","0804","工学","材料类","本科","材料科学与工程","080401");
        addUndergraduate("08","0804","工学","材料类","本科","材料物理","080402");
        addUndergraduate("08","0804","工学","材料类","本科","材料化学","080403");
        addUndergraduate("08","0804","工学","材料类","本科","冶金工程","080404");
        addUndergraduate("08","0804","工学","材料类","本科","金属材料工程","080405");
        addUndergraduate("08","0804","工学","材料类","本科","无机非金属材料工程","080406");
        addUndergraduate("08","0804","工学","材料类","本科","高分子材料与工程","080407");
        addUndergraduate("08","0804","工学","材料类","本科","复合材料与工程","080408T");
        addUndergraduate("08","0804","工学","材料类","本科","粉体材料科学与工程","080409T");
        addUndergraduate("08","0804","工学","材料类","本科","宝石及材料工艺学","080410T");
        addUndergraduate("08","0804","工学","材料类","本科","焊接技术与工程","080411T");
        addUndergraduate("08","0804","工学","材料类","本科","功能材料","080412T");
        addUndergraduate("08","0804","工学","材料类","本科","纳米材料与技术","080413T");
        addUndergraduate("08","0804","工学","材料类","本科","新能源材料与器件","080414T");
        addUndergraduate("08","0804","工学","材料类","本科","材料设计科学与工程","080415T");
        addUndergraduate("08","0804","工学","材料类","本科","复合材料成型工程","080416T");
        addUndergraduate("08","0804","工学","材料类","本科","智能材料与结构","080417T");
        addUndergraduate("08","0805","工学","能源动力类","本科","能源与动力工程","080501");
        addUndergraduate("08","0805","工学","能源动力类","本科","能源与环境系统工程","080502T");
        addUndergraduate("08","0805","工学","能源动力类","本科","新能源科学与工程","080503T");
        addUndergraduate("08","0805","工学","能源动力类","本科","储能科学与工程","080504T");
        addUndergraduate("08","0806","工学","电气类","本科","电气工程及其自动化","080601");
        addUndergraduate("08","0806","工学","电气类","本科","智能电网信息工程","080602T");
        addUndergraduate("08","0806","工学","电气类","本科","光源与照明","080603T");
        addUndergraduate("08","0806","工学","电气类","本科","电气工程与智能控制","080604T");
        addUndergraduate("08","0806","工学","电气类","本科","电机电器智能化","080605T");
        addUndergraduate("08","0806","工学","电气类","本科","电缆工程","080606T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电子信息工程","080701");
        addUndergraduate("08","0807","工学","电子信息类","本科","电子科学与技术","080702");
        addUndergraduate("08","0807","工学","电子信息类","本科","通信工程","080703");
        addUndergraduate("08","0807","工学","电子信息类","本科","微电子科学与工程","080704");
        addUndergraduate("08","0807","工学","电子信息类","本科","光电信息科学与工程","080705");
        addUndergraduate("08","0807","工学","电子信息类","本科","信息工程","080706");
        addUndergraduate("08","0807","工学","电子信息类","本科","广播电视工程","080707T");
        addUndergraduate("08","0807","工学","电子信息类","本科","水声工程","080708T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电子封装技术","080709T");
        addUndergraduate("08","0807","工学","电子信息类","本科","集成电路设计与集成系统","080710T");
        addUndergraduate("08","0807","工学","电子信息类","本科","医学信息工程","080711T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电磁场与无线技术","080712T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电波传播与天线","080713T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电子信息科学与技术","080714T");
        addUndergraduate("08","0807","工学","电子信息类","本科","电信工程及管理","080715T");
        addUndergraduate("08","0807","工学","电子信息类","本科","应用电子技术教育","080716T");
        addUndergraduate("08","0807","工学","电子信息类","本科","人工智能","080717T");
        addUndergraduate("08","0807","工学","电子信息类","本科","海洋信息工程","080718T");
        addUndergraduate("08","0807","工学","电子信息类","本科","柔性电子学","080719T");
        addUndergraduate("08","0808","工学","自动化类","本科","自动化","080801");
        addUndergraduate("08","0808","工学","自动化类","本科","轨道交通信号与控制","080802T");
        addUndergraduate("08","0808","工学","自动化类","本科","机器人工程","080803T");
        addUndergraduate("08","0808","工学","自动化类","本科","邮政工程","080804T");
        addUndergraduate("08","0808","工学","自动化类","本科","核电技术与控制工程","080805T");
        addUndergraduate("08","0808","工学","自动化类","本科","智能装备与系统","080806T");
        addUndergraduate("08","0809","工学","计算机类","本科","计算机科学与技术","080901");
        addUndergraduate("08","0809","工学","计算机类","本科","软件工程","080902");
        addUndergraduate("08","0809","工学","计算机类","本科","网络工程","080903");
        addUndergraduate("08","0809","工学","计算机类","本科","信息安全","080904K");
        addUndergraduate("08","0809","工学","计算机类","本科","物联网工程","080905");
        addUndergraduate("08","0809","工学","计算机类","本科","数字媒体技术","080906");
        addUndergraduate("08","0809","工学","计算机类","本科","智能科学与技术","080907T");
        addUndergraduate("08","0809","工学","计算机类","本科","空间信息与数字技术","080908T");
        addUndergraduate("08","0809","工学","计算机类","本科","电子与计算机工程","080909T");
        addUndergraduate("08","0809","工学","计算机类","本科","数据科学与大数据技术","080910T");
        addUndergraduate("08","0809","工学","计算机类","本科","网络空间安全","080911TK");
        addUndergraduate("08","0809","工学","计算机类","本科","新媒体技术","080912T");
        addUndergraduate("08","0809","工学","计算机类","本科","电影制作","080913T");
        addUndergraduate("08","0809","工学","计算机类","本科","保密技术","080914TK");
        addUndergraduate("08","0809","工学","计算机类","本科","服务科学与工程","080915T");
        addUndergraduate("08","0809","工学","计算机类","本科","虚拟现实技术","080916T");
        addUndergraduate("08","0809","工学","计算机类","本科","区块链工程","080917T");
        addUndergraduate("08","0810","工学","土木类","本科","土木工程","081001");
        addUndergraduate("08","0810","工学","土木类","本科","建筑环境与能源应用工程","081002");
        addUndergraduate("08","0810","工学","土木类","本科","给排水科学与工程","081003");
        addUndergraduate("08","0810","工学","土木类","本科","建筑电气与智能化","081004T");
        addUndergraduate("08","0810","工学","土木类","本科","道路桥梁与渡河工程","081005T");
        addUndergraduate("08","0810","工学","土木类","本科","城市地下空间工程","081006T");
        addUndergraduate("08","0810","工学","土木类","本科","智能建造","081007T");
        addUndergraduate("08","0810","工学","土木类","本科","土木、水利与海洋工程","081008T");
        addUndergraduate("08","0811","工学","水利类","本科","水利水电工程","081101");
        addUndergraduate("08","0811","工学","水利类","本科","水文与水资源工程","081102");
        addUndergraduate("08","0811","工学","水利类","本科","港口航道与海岸工程","081103");
        addUndergraduate("08","0811","工学","水利类","本科","水务工程","081104T");
        addUndergraduate("08","0811","工学","水利类","本科","智慧水利","081105T");
        addUndergraduate("08","0812","工学","测绘类","本科","测绘工程","081201");
        addUndergraduate("08","0812","工学","测绘类","本科","遥感科学与技术","081202");
        addUndergraduate("08","0812","工学","测绘类","本科","导航工程","081203T");
        addUndergraduate("08","0812","工学","测绘类","本科","地理国情监测","081204T");
        addUndergraduate("08","0812","工学","测绘类","本科","地理空间信息工程","081205T");
        addUndergraduate("08","0813","工学","化工与制药类","本科","化学工程与工艺","081301");
        addUndergraduate("08","0813","工学","化工与制药类","本科","制药工程","081302");
        addUndergraduate("08","0813","工学","化工与制药类","本科","资源循环科学与工程","081303T");
        addUndergraduate("08","0813","工学","化工与制药类","本科","能源化学工程","081304T");
        addUndergraduate("08","0813","工学","化工与制药类","本科","化学工程与工业生物工程","081305T");
        addUndergraduate("08","0813","工学","化工与制药类","本科","精细化工","081306T");
        addUndergraduate("08","0813","工学","化工与制药类","本科","海洋油气工程","081307T");
        addUndergraduate("08","0814","工学","地质类","本科","地质工程","081401");
        addUndergraduate("08","0814","工学","地质类","本科","勘查技术与工程","081402");
        addUndergraduate("08","0814","工学","地质类","本科","资源勘查工程","081403");
        addUndergraduate("08","0814","工学","地质类","本科","地下水科学与工程","081404T");
        addUndergraduate("08","0814","工学","地质类","本科","旅游地学与规划工程","081405T");
        addUndergraduate("08","0815","工学","矿业类","本科","采矿工程","081501");
        addUndergraduate("08","0815","工学","矿业类","本科","石油工程","081502");
        addUndergraduate("08","0815","工学","矿业类","本科","矿物加工工程","081503");
        addUndergraduate("08","0815","工学","矿业类","本科","油气储运工程","081504");
        addUndergraduate("08","0815","工学","矿业类","本科","矿物资源工程","081505T");
        addUndergraduate("08","0815","工学","矿业类","本科","海洋油气工程","081506T");
        addUndergraduate("08","0815","工学","矿业类","本科","智能采矿工程","081507T");
        addUndergraduate("08","0816","工学","纺织类","本科","纺织工程","081601");
        addUndergraduate("08","0816","工学","纺织类","本科","服装设计与工程","081602");
        addUndergraduate("08","0816","工学","纺织类","本科","非织造材料与工程","081603T");
        addUndergraduate("08","0816","工学","纺织类","本科","服装设计与工艺教育","081604T");
        addUndergraduate("08","0816","工学","纺织类","本科","丝绸设计与工程","081605T");
        addUndergraduate("08","0817","工学","轻工类","本科","轻化工程","081701");
        addUndergraduate("08","0817","工学","轻工类","本科","包装工程","081702");
        addUndergraduate("08","0817","工学","轻工类","本科","印刷工程","081703");
        addUndergraduate("08","0817","工学","轻工类","本科","香料香精技术与工程","081704T");
        addUndergraduate("08","0817","工学","轻工类","本科","化妆品技术与工程","081705T");
        addUndergraduate("08","0818","工学","交通运输类","本科","交通运输","081801");
        addUndergraduate("08","0818","工学","交通运输类","本科","交通工程","081802");
        addUndergraduate("08","0818","工学","交通运输类","本科","航海技术","081803");
        addUndergraduate("08","0818","工学","交通运输类","本科","轮机工程","081804");
        addUndergraduate("08","0818","工学","交通运输类","本科","飞行技术","081805");
        addUndergraduate("08","0818","工学","交通运输类","本科","交通设备与控制工程","081806T");
        addUndergraduate("08","0818","工学","交通运输类","本科","救助与打捞工程","081807T");
        addUndergraduate("08","0818","工学","交通运输类","本科","船舶电子电气工程","081808T");
        addUndergraduate("08","0818","工学","交通运输类","本科","轨道交通电气与控制","081809T");
        addUndergraduate("08","0818","工学","交通运输类","本科","邮轮工程与管理","081810T");
        addUndergraduate("08","0819","工学","海洋工程类","本科","船舶与海洋工程","081901");
        addUndergraduate("08","0819","工学","海洋工程类","本科","海洋工程与技术","081902T");
        addUndergraduate("08","0819","工学","海洋工程类","本科","海洋资源开发技术","081903T");
        addUndergraduate("08","0820","工学","航空航天类","本科","航空航天工程","082001");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器设计与工程","082002");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器制造工程","082003");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器动力工程","082004");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器环境与生命保障工程","082005");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器质量与可靠性","082006T");
        addUndergraduate("08","0820","工学","航空航天类","本科","飞行器适航技术","082007T");
        addUndergraduate("08","0820","工学","航空航天类","本科","无人驾驶航空器系统工程","082008T");
        addUndergraduate("08","0820","工学","航空航天类","本科","智能飞行器技术","082009T");
        addUndergraduate("08","0821","工学","兵器类","本科","武器系统与工程","082101");
        addUndergraduate("08","0821","工学","兵器类","本科","武器发射工程","082102");
        addUndergraduate("08","0821","工学","兵器类","本科","探测制导与控制技术","082103");
        addUndergraduate("08","0821","工学","兵器类","本科","弹药工程与爆炸技术","082104");
        addUndergraduate("08","0821","工学","兵器类","本科","特种能源技术与工程","082105");
        addUndergraduate("08","0821","工学","兵器类","本科","装甲车辆工程","082106");
        addUndergraduate("08","0821","工学","兵器类","本科","信息对抗技术","082107T");
        addUndergraduate("08","0821","工学","兵器类","本科","智能无人系统技术","082108T");
        addUndergraduate("08","0822","工学","核工程类","本科","核工程与核技术","082201");
        addUndergraduate("08","0822","工学","核工程类","本科","辐射防护与核安全","082202");
        addUndergraduate("08","0822","工学","核工程类","本科","工程物理","082203");
        addUndergraduate("08","0822","工学","核工程类","本科","核化工与核燃料工程","082204T");
        addUndergraduate("08","0823","工学","农业工程类","本科","农业工程","082301");
        addUndergraduate("08","0823","工学","农业工程类","本科","农业机械化及其自动化","082302");
        addUndergraduate("08","0823","工学","农业工程类","本科","农业电气化","082303T");
        addUndergraduate("08","0823","工学","农业工程类","本科","农业建筑环境与能源工程","082304T");
        addUndergraduate("08","0823","工学","农业工程类","本科","农业水利工程","082305T");
        addUndergraduate("08","0823","工学","农业工程类","本科","土地整治工程","082306T");
        addUndergraduate("08","0824","工学","林业工程类","本科","森林工程","082401");
        addUndergraduate("08","0824","工学","林业工程类","本科","木材科学与工程","082402");
        addUndergraduate("08","0824","工学","林业工程类","本科","林产化工","082403");
        addUndergraduate("08","0824","工学","林业工程类","本科","家具设计与工程","082404T");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","环境科学与工程","082501");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","环境工程","082502");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","环境科学","082503");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","环境生态工程","082504T");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","环保设备工程","082505T");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","资源环境科学","082506T");
        addUndergraduate("08","0825","工学","环境科学与工程类","本科","水质科学与技术","082507T");
        addUndergraduate("08","0826","工学","生物医学工程类","本科","生物医学工程","082601");
        addUndergraduate("08","0826","工学","生物医学工程类","本科","假肢矫形工程","082602T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","食品科学与工程","082701");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","食品质量与安全","082702");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","粮食工程","082703T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","乳品工程","082704T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","酿酒工程","082705T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","葡萄与葡萄酒工程","082706T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","食品营养与检验教育","082707T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","烹饪与营养教育","082708T");
        addUndergraduate("08","0827","工学","食品科学与工程类","本科","食品卫生与营养学","082709T");
        addUndergraduate("08","0828","工学","建筑类","本科","建筑学","082801");
        addUndergraduate("08","0828","工学","建筑类","本科","城乡规划","082802");
        addUndergraduate("08","0828","工学","建筑类","本科","风景园林","082803");
        addUndergraduate("08","0828","工学","建筑类","本科","历史建筑保护工程","082804T");
        addUndergraduate("08","0828","工学","建筑类","本科","人居环境科学与技术","082805T");
        addUndergraduate("08","0829","工学","安全科学与工程类","本科","安全工程","082901");
        addUndergraduate("08","0829","工学","安全科学与工程类","本科","应急技术与管理","082902T");
        addUndergraduate("08","0830","工学","生物工程类","本科","生物工程","083001");
        addUndergraduate("08","0830","工学","生物工程类","本科","生物制药","083002T");
        addUndergraduate("08","0831","工学","公安技术类","本科","刑事科学技术","083101K");
        addUndergraduate("08","0831","工学","公安技术类","本科","消防工程","083102K");
        addUndergraduate("08","0831","工学","公安技术类","本科","交通管理工程","083103K");
        addUndergraduate("08","0831","工学","公安技术类","本科","安全防范工程","083104K");
        addUndergraduate("08","0831","工学","公安技术类","本科","公安视听技术","083105K");
        addUndergraduate("08","0831","工学","公安技术类","本科","网络安全与执法","083106K");
        addUndergraduate("08","0831","工学","公安技术类","本科","核生化消防","083107T");
        addUndergraduate("09","0901","农学","植物生产类","本科","农学","090101");
        addUndergraduate("09","0901","农学","植物生产类","本科","园艺","090102");
        addUndergraduate("09","0901","农学","植物生产类","本科","植物保护","090103");
        addUndergraduate("09","0901","农学","植物生产类","本科","植物科学与技术","090104");
        addUndergraduate("09","0901","农学","植物生产类","本科","种子科学与工程","090105");
        addUndergraduate("09","0901","农学","植物生产类","本科","设施农业科学与工程","090106T");
        addUndergraduate("09","0901","农学","植物生产类","本科","茶学","090107T");
        addUndergraduate("09","0901","农学","植物生产类","本科","烟草","090108T");
        addUndergraduate("09","0901","农学","植物生产类","本科","应用生物科学","090109T");
        addUndergraduate("09","0901","农学","植物生产类","本科","农艺教育","090110T");
        addUndergraduate("09","0901","农学","植物生产类","本科","园艺教育","090111T");
        addUndergraduate("09","0901","农学","植物生产类","本科","菌物科学与工程","090112T");
        addUndergraduate("09","0901","农学","植物生产类","本科","农药化肥","090113T");
        addUndergraduate("09","0902","农学","自然保护与环境生态类","本科","农业资源与环境","090201");
        addUndergraduate("09","0902","农学","自然保护与环境生态类","本科","野生动物与自然保护区管理","090202");
        addUndergraduate("09","0902","农学","自然保护与环境生态类","本科","水土保持与荒漠化防治","090203");
        addUndergraduate("09","0902","农学","自然保护与环境生态类","本科","土地资源管理","090204");
        addUndergraduate("09","0903","农学","动物生产类","本科","动物科学","090301");
        addUndergraduate("09","0903","农学","动物生产类","本科","蚕学","090302");
        addUndergraduate("09","0903","农学","动物生产类","本科","蜂学","090303T");
        addUndergraduate("09","0903","农学","动物生产类","本科","经济动物学","090304T");
        addUndergraduate("09","0903","农学","动物生产类","本科","马业科学","090305T");
        addUndergraduate("09","0904","农学","动物医学类","本科","动物医学","090401");
        addUndergraduate("09","0904","农学","动物医学类","本科","动物药学","090402T");
        addUndergraduate("09","0904","农学","动物医学类","本科","动植物检疫","090403T");
        addUndergraduate("09","0904","农学","动物医学类","本科","实验动物学","090404T");
        addUndergraduate("09","0904","农学","动物医学类","本科","中兽医学","090405T");
        addUndergraduate("09","0905","农学","林学类","本科","林学","090501");
        addUndergraduate("09","0905","农学","林学类","本科","园林","090502");
        addUndergraduate("09","0905","农学","林学类","本科","森林保护","090503");
        addUndergraduate("09","0905","农学","林学类","本科","经济林","090504T");
        addUndergraduate("09","0906","农学","水产类","本科","水产养殖学","090601");
        addUndergraduate("09","0906","农学","水产类","本科","海洋渔业科学与技术","090602");
        addUndergraduate("09","0906","农学","水产类","本科","水族科学与技术","090603T");
        addUndergraduate("09","0906","农学","水产类","本科","水生动物医学","090604T");
        addUndergraduate("09","0907","农学","草学类","本科","草业科学","090701");
        addUndergraduate("10","1001","医学","基础医学类","本科","基础医学","100101K");
        addUndergraduate("10","1002","医学","临床医学类","本科","临床医学","100201K");
        addUndergraduate("10","1002","医学","临床医学类","本科","麻醉学","100202K");
        addUndergraduate("10","1002","医学","临床医学类","本科","医学影像学","100203K");
        addUndergraduate("10","1002","医学","临床医学类","本科","眼视光医学","100204K");
        addUndergraduate("10","1002","医学","临床医学类","本科","精神医学","100205K");
        addUndergraduate("10","1002","医学","临床医学类","本科","放射医学","100206K");
        addUndergraduate("10","1002","医学","临床医学类","本科","儿科学","100207K");
        addUndergraduate("10","1003","医学","口腔医学类","本科","口腔医学","100301K");
        addUndergraduate("10","1004","医学","公共卫生与预防医学类","本科","预防医学","100401K");
        addUndergraduate("10","1004","医学","公共卫生与预防医学类","本科","食品卫生与营养学","100402K");
        addUndergraduate("10","1004","医学","公共卫生与预防医学类","本科","妇幼保健医学","100403TK");
        addUndergraduate("10","1004","医学","公共卫生与预防医学类","本科","卫生监督","100404TK");
        addUndergraduate("10","1004","医学","公共卫生与预防医学类","本科","全球健康学","100405TK");
        addUndergraduate("10","1005","医学","中医学类","本科","中医学","100501K");
        addUndergraduate("10","1005","医学","中医学类","本科","针灸推拿学","100502K");
        addUndergraduate("10","1005","医学","中医学类","本科","藏医学","100503K");
        addUndergraduate("10","1005","医学","中医学类","本科","蒙医学","100504K");
        addUndergraduate("10","1005","医学","中医学类","本科","维医学","100505K");
        addUndergraduate("10","1005","医学","中医学类","本科","壮医学","100506K");
        addUndergraduate("10","1005","医学","中医学类","本科","哈医学","100507K");
        addUndergraduate("10","1005","医学","中医学类","本科","傣医学","100508K");
        addUndergraduate("10","1005","医学","中医学类","本科","回医学","100509K");
        addUndergraduate("10","1005","医学","中医学类","本科","中医康复学","100510K");
        addUndergraduate("10","1005","医学","中医学类","本科","中医养生学","100511K");
        addUndergraduate("10","1005","医学","中医学类","本科","中医儿科学","100512K");
        addUndergraduate("10","1005","医学","中医学类","本科","中医骨伤科学","100513K");
        addUndergraduate("10","1006","医学","中西医结合类","本科","中西医临床医学","100601K");
        addUndergraduate("10","1007","医学","药学类","本科","药学","100701");
        addUndergraduate("10","1007","医学","药学类","本科","药物制剂","100702");
        addUndergraduate("10","1007","医学","药学类","本科","临床药学","100703T");
        addUndergraduate("10","1007","医学","药学类","本科","药事管理","100704T");
        addUndergraduate("10","1007","医学","药学类","本科","药物分析","100705T");
        addUndergraduate("10","1007","医学","药学类","本科","药物化学","100706T");
        addUndergraduate("10","1007","医学","药学类","本科","海洋药学","100707T");
        addUndergraduate("10","1007","医学","药学类","本科","化妆品科学与技术","100708T");
        addUndergraduate("10","1008","医学","中药学类","本科","中药学","100801");
        addUndergraduate("10","1008","医学","中药学类","本科","中药资源与开发","100802");
        addUndergraduate("10","1008","医学","中药学类","本科","藏药学","100803T");
        addUndergraduate("10","1008","医学","中药学类","本科","蒙药学","100804T");
        addUndergraduate("10","1008","医学","中药学类","本科","中药制药","100805T");
        addUndergraduate("10","1008","医学","中药学类","本科","中草药栽培与鉴定","100806T");
        addUndergraduate("10","1009","医学","法医学类","本科","法医学","100901K");
        addUndergraduate("10","1010","医学","医学技术类","本科","医学检验技术","101001");
        addUndergraduate("10","1010","医学","医学技术类","本科","医学实验技术","101002");
        addUndergraduate("10","1010","医学","医学技术类","本科","医学影像技术","101003");
        addUndergraduate("10","1010","医学","医学技术类","本科","眼视光学","101004");
        addUndergraduate("10","1010","医学","医学技术类","本科","康复治疗学","101005");
        addUndergraduate("10","1010","医学","医学技术类","本科","口腔医学技术","101006");
        addUndergraduate("10","1010","医学","医学技术类","本科","卫生检验与检疫","101007");
        addUndergraduate("10","1010","医学","医学技术类","本科","听力与言语康复学","101008T");
        addUndergraduate("10","1010","医学","医学技术类","本科","康复物理治疗","101009T");
        addUndergraduate("10","1010","医学","医学技术类","本科","康复作业治疗","101010T");
        addUndergraduate("10","1010","医学","医学技术类","本科","智能医学工程","101011T");
        addUndergraduate("10","1010","医学","医学技术类","本科","生物医药数据科学","101012T");
        addUndergraduate("10","1011","医学","护理学类","本科","护理学","101101");
        addUndergraduate("10","1011","医学","护理学类","本科","助产学","101102T");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","管理科学","120101");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","信息管理与信息系统","120102");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","工程管理","120103");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","房地产开发与管理","120104");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","工程造价","120105T");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","保密管理","120106TK");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","邮政管理","120107T");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","大数据管理与应用","120108T");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","工程审计","120109T");
        addUndergraduate("12","1201","管理学","管理科学与工程类","本科","计算金融","120110T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","工商管理","120201K");
        addUndergraduate("12","1202","管理学","工商管理类","本科","市场营销","120202");
        addUndergraduate("12","1202","管理学","工商管理类","本科","会计学","120203K");
        addUndergraduate("12","1202","管理学","工商管理类","本科","财务管理","120204");
        addUndergraduate("12","1202","管理学","工商管理类","本科","国际商务","120205");
        addUndergraduate("12","1202","管理学","工商管理类","本科","人力资源管理","120206");
        addUndergraduate("12","1202","管理学","工商管理类","本科","审计学","120207");
        addUndergraduate("12","1202","管理学","工商管理类","本科","资产评估","120208T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","物业管理","120209T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","文化产业管理","120210T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","劳动关系","120211T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","体育经济与管理","120212T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","财务会计教育","120213T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","市场营销教育","120214T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","零售业管理","120215T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","特许经营管理","120216T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","互联网金融","120217T");
        addUndergraduate("12","1202","管理学","工商管理类","本科","商务智能","120218T");
        addUndergraduate("12","1203","管理学","农业经济管理类","本科","农林经济管理","120301");
        addUndergraduate("12","1203","管理学","农业经济管理类","本科","农村区域发展","120302");
        addUndergraduate("12","1204","管理学","公共管理类","本科","公共事业管理","120401");
        addUndergraduate("12","1204","管理学","公共管理类","本科","行政管理","120402");
        addUndergraduate("12","1204","管理学","公共管理类","本科","劳动与社会保障","120403");
        addUndergraduate("12","1204","管理学","公共管理类","本科","土地资源管理","120404");
        addUndergraduate("12","1204","管理学","公共管理类","本科","城市管理","120405T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","海关管理","120406T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","交通管理","120407T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","海事管理","120408T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","公共关系学","120409T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","健康服务与管理","120410T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","海警后勤管理","120411T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","应急管理","120412T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","医疗产品管理","120413T");
        addUndergraduate("12","1204","管理学","公共管理类","本科","医疗保险","120414T");
        addUndergraduate("12","1205","管理学","图书情报与档案管理类","本科","图书馆学","120501");
        addUndergraduate("12","1205","管理学","图书情报与档案管理类","本科","档案学","120502");
        addUndergraduate("12","1205","管理学","图书情报与档案管理类","本科","信息资源管理","120503T");
        addUndergraduate("12","1206","管理学","物流管理与工程类","本科","物流管理","120601");
        addUndergraduate("12","1206","管理学","物流管理与工程类","本科","物流工程","120602T");
        addUndergraduate("12","1206","管理学","物流管理与工程类","本科","采购管理","120603T");
        addUndergraduate("12","1207","管理学","工业工程类","本科","工业工程","120701");
        addUndergraduate("12","1207","管理学","工业工程类","本科","标准化工程","120702T");
        addUndergraduate("12","1207","管理学","工业工程类","本科","质量管理工程","120703T");
        addUndergraduate("12","1208","管理学","电子商务类","本科","电子商务","120801");
        addUndergraduate("12","1208","管理学","电子商务类","本科","电子商务及法律","120802T");
        addUndergraduate("12","1209","管理学","旅游管理类","本科","旅游管理","120901K");
        addUndergraduate("12","1209","管理学","旅游管理类","本科","酒店管理","120902");
        addUndergraduate("12","1209","管理学","旅游管理类","本科","会展经济与管理","120903");
        addUndergraduate("12","1209","管理学","旅游管理类","本科","旅游管理与服务教育","120904T");
        addUndergraduate("13","1301","艺术学","艺术学理论类","本科","艺术史论","130101");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","音乐表演","130201");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","音乐学","130202");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","作曲与作曲技术理论","130203");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","舞蹈表演","130204");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","舞蹈学","130205");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","舞蹈编导","130206");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","舞蹈教育","130207T");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","航空服务艺术与管理","130208T");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","流行音乐","130209T");
        addUndergraduate("13","1302","艺术学","音乐与舞蹈学类","本科","音乐治疗","130210T");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","表演","130301");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","戏剧学","130302");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","电影学","130303");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","戏剧影视文学","130304");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","广播电视编导","130305");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","戏剧影视导演","130306");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","戏剧影视美术设计","130307");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","录音艺术","130308");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","播音与主持艺术","130309");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","动画","130310");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","影视摄影与制作","130311T");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","影视技术","130312T");
        addUndergraduate("13","1303","艺术学","戏剧与影视学类","本科","戏剧教育","130313T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","美术学","130401");
        addUndergraduate("13","1304","艺术学","美术学类","本科","绘画","130402");
        addUndergraduate("13","1304","艺术学","美术学类","本科","雕塑","130403");
        addUndergraduate("13","1304","艺术学","美术学类","本科","摄影","130404");
        addUndergraduate("13","1304","艺术学","美术学类","本科","书法学","130405T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","中国画","130406T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","实验艺术","130407T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","跨媒体艺术","130408T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","文物保护与修复","130409T");
        addUndergraduate("13","1304","艺术学","美术学类","本科","漫画","130410T");
        addUndergraduate("13","1305","艺术学","设计学类","本科","艺术设计学","130501");
        addUndergraduate("13","1305","艺术学","设计学类","本科","视觉传达设计","130502");
        addUndergraduate("13","1305","艺术学","设计学类","本科","环境设计","130503");
        addUndergraduate("13","1305","艺术学","设计学类","本科","产品设计","130504");
        addUndergraduate("13","1305","艺术学","设计学类","本科","服装与服饰设计","130505");
        addUndergraduate("13","1305","艺术学","设计学类","本科","公共艺术","130506");
        addUndergraduate("13","1305","艺术学","设计学类","本科","工艺美术","130507");
        addUndergraduate("13","1305","艺术学","设计学类","本科","数字媒体艺术","130508");
        addUndergraduate("13","1305","艺术学","设计学类","本科","艺术与科技","130509T");
        addUndergraduate("13","1305","艺术学","设计学类","本科","陶瓷艺术设计","130510T");
        addUndergraduate("13","1305","艺术学","设计学类","本科","新媒体艺术","130511T");
        addUndergraduate("13","1305","艺术学","设计学类","本科","包装设计","130512T");
    }

    /**
     * 初始化研究生专业数据
     */
    private static void initializeGraduateMajors() {
        addGraduate("01", null, "哲学", null, "学硕", "哲学", "01", 1);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "哲学", "0101", 2);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "马克思主义哲学", "010101", 3);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "中国哲学", "010102", 4);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "外国哲学", "010103", 5);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "逻辑学", "010104", 6);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "伦理学", "010105", 7);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "美学", "010106", 8);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "宗教学", "010107", 9);
        addGraduate("01", "0101", "哲学", "哲学", "学硕", "科学技术哲学", "010108", 10);
        addGraduate("01", "0151", "哲学", "应用伦理", "专硕", "应用伦理", "0151", 11);
        addGraduate("02", null, "经济学", null, "学硕", "经济学", "02", 12);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "理论经济学", "0201", 13);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "政治经济学", "020101", 14);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "经济思想史", "020102", 15);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "经济史", "020103", 16);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "西方经济学", "020104", 17);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "世界经济", "020105", 18);
        addGraduate("02", "0201", "经济学", "理论经济学", "学硕", "人口、资源与环境经济学", "020106", 19);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "应用经济学", "0202", 20);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "国民经济学", "020201", 21);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "区域经济学", "020202", 22);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "财政学", "020203", 23);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "金融学", "020204", 24);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "产业经济学", "020205", 25);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "国际贸易学", "020206", 26);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "劳动经济学", "020207", 27);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "统计学", "020208", 28);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "数量经济学", "020209", 29);
        addGraduate("02", "0202", "经济学", "应用经济学", "学硕", "国防经济", "020210", 30);
        addGraduate("02", "0270", "经济学", "统计学", "学硕", "统计学", "0270", 31);
        addGraduate("02", "0271", "经济学", "区域国别学", "学硕", "区域国别学", "0271", 32);
        addGraduate("02", "0251", "经济学", "金融", "专硕", "金融", "0251", 33);
        addGraduate("02", "0252", "经济学", "应用统计", "专硕", "应用统计", "0252", 34);
        addGraduate("02", "0253", "经济学", "税务", "专硕", "税务", "0253", 35);
        addGraduate("02", "0254", "经济学", "国际商务", "专硕", "国际商务", "0254", 36);
        addGraduate("02", "0255", "经济学", "保险", "专硕", "保险", "0255", 37);
        addGraduate("02", "0256", "经济学", "资产评估", "专硕", "资产评估", "0256", 38);
        addGraduate("02", "0258", "经济学", "数字经济", "专硕", "数字经济", "0258", 39);
        addGraduate("03", null, "法学", null, "学硕", "法学", "03", 40);
        addGraduate("03", "0301", "法学", "法学", "学硕", "法学", "0301", 41);
        addGraduate("03", "0301", "法学", "法学", "学硕", "法学理论", "030101", 42);
        addGraduate("03", "0301", "法学", "法学", "学硕", "法律史", "030102", 43);
        addGraduate("03", "0301", "法学", "法学", "学硕", "宪法学与行政法学", "030103", 44);
        addGraduate("03", "0301", "法学", "法学", "学硕", "刑法学", "030104", 45);
        addGraduate("03", "0301", "法学", "法学", "学硕", "民商法学", "030105", 46);
        addGraduate("03", "0301", "法学", "法学", "学硕", "诉讼法学", "030106", 47);
        addGraduate("03", "0301", "法学", "法学", "学硕", "经济法学", "030107", 48);
        addGraduate("03", "0301", "法学", "法学", "学硕", "环境与资源保护法学", "030108", 49);
        addGraduate("03", "0301", "法学", "法学", "学硕", "国际法学", "030109", 50);
        addGraduate("03", "0301", "法学", "法学", "学硕", "军事法学", "030110", 51);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "政治学", "0302", 52);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "政治学理论", "030201", 53);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "中外政治制度", "030202", 54);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "科学社会主义与国际共产主义运动", "030203", 55);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "中共党史", "030204", 56);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "国际政治", "030206", 57);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "国际关系", "030207", 58);
        addGraduate("03", "0302", "法学", "政治学", "学硕", "外交学", "030208", 59);
        addGraduate("03", "0303", "法学", "社会学", "学硕", "社会学", "0303", 60);
        addGraduate("03", "0303", "法学", "社会学", "学硕", "社会学", "030301", 61);
        addGraduate("03", "0303", "法学", "社会学", "学硕", "人口学", "030302", 62);
        addGraduate("03", "0303", "法学", "社会学", "学硕", "人类学", "030303", 63);
        addGraduate("03", "0303", "法学", "社会学", "学硕", "民俗学", "030304", 64);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "民族学", "0304", 65);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "民族学", "030401", 66);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "马克思主义民族理论与政策", "030402", 67);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "中国少数民族经济", "030403", 68);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "中国少数民族史", "030404", 69);
        addGraduate("03", "0304", "法学", "民族学", "学硕", "中国少数民族艺术", "030405", 70);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "马克思主义理论", "0305", 71);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "马克思主义基本原理", "030501", 72);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "马克思主义发展史", "030502", 73);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "马克思主义中国化研究", "030503", 74);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "国外马克思主义研究", "030504", 75);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "思想政治教育", "030505", 76);
        addGraduate("03", "0305", "法学", "马克思主义理论", "学硕", "中国近现代史基本问题研究", "030506", 77);
        addGraduate("03", "0306", "法学", "公安学", "学硕", "公安学", "0306", 78);
        addGraduate("03", "0307", "法学", "中共党史党建学", "学硕", "中共党史党建学", "0307", 79);
        addGraduate("03", "0308", "法学", "纪检监察学", "学硕", "纪检监察学", "0308", 80);
        addGraduate("03", "0370", "法学", "国家安全学", "学硕", "国家安全学", "0370", 81);
        addGraduate("03", "0371", "法学", "区域国别学", "学硕", "区域国别学", "0371", 82);
        addGraduate("03", "0351", "法学", "法律", "学硕", "法律", "0351", 83);
        addGraduate("03", "0351", "法学", "法律", "学硕", "法律(非法学)", "035101", 84);
        addGraduate("03", "0351", "法学", "法律", "学硕", "法律(法学)", "035102", 85);
        addGraduate("03", "0352", "法学", "社会工作", "学硕", "社会工作", "0352", 86);
        addGraduate("03", "0353", "法学", "警务", "专硕", "警务", "0353", 87);
        addGraduate("03", "0354", "法学", "知识产权", "专硕", "知识产权", "0354", 88);
        addGraduate("03", "0355", "法学", "国际事务", "专硕", "国际事务", "0355", 89);
        addGraduate("04", null, "教育学", null, "学硕", "教育学", "04", 90);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "教育学", "0401", 91);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "教育学原理", "040101", 92);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "课程与教学论", "040102", 93);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "教育史", "040103", 94);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "比较教育学", "040104", 95);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "学前教育学", "040105", 96);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "高等教育学", "040106", 97);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "成人教育学", "040107", 98);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "职业技术教育学", "040108", 99);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "特殊教育学", "040109", 100);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "教育技术学", "040110", 101);
        addGraduate("04", "0401", "教育学", "教育学", "学硕", "教育法学", "040111", 102);
        addGraduate("04", "0402", "教育学", "心理学", "学硕", "心理学", "0402", 103);
        addGraduate("04", "0402", "教育学", "心理学", "学硕", "基础心理学", "040201", 104);
        addGraduate("04", "0402", "教育学", "心理学", "学硕", "发展与教育心理学", "040202", 105);
        addGraduate("04", "0402", "教育学", "心理学", "学硕", "应用心理学", "040203", 106);
        addGraduate("04", "0403", "教育学", "体育学", "学硕", "体育学", "0403", 107);
        addGraduate("04", "0403", "教育学", "体育学", "学硕", "体育人文社会学", "040301", 108);
        addGraduate("04", "0403", "教育学", "体育学", "学硕", "运动人体科学", "040302", 109);
        addGraduate("04", "0403", "教育学", "体育学", "学硕", "体育教育训练学", "040303", 110);
        addGraduate("04", "0403", "教育学", "体育学", "学硕", "民族传统体育学", "040304", 111);
        addGraduate("04", null, "教育学", null, "学硕", "教育经济与管理", "047101", 112);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "教育", "0451", 113);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "教育管理", "045101", 114);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(思政)", "045102", 115);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(语文)", "045103", 116);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(数学)", "045104", 117);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(物理)", "045105", 118);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(化学)", "045106", 119);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(生物)", "045107", 120);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(英语)", "045108", 121);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(历史)", "045109", 122);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(地理)", "045110", 123);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(音乐)", "045111", 124);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(体育)", "045112", 125);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学科教学(美术)", "045113", 126);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "现代教育技术", "045114", 127);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "小学教育", "045115", 128);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "心理健康教育", "045116", 129);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "科学与技术教育", "045117", 130);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "学前教育", "045118", 131);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "特殊教育", "045119", 132);
        addGraduate("04", "0451", "教育学", "教育", "学硕", "职业技术教育", "045120", 133);
        addGraduate("04", "0451", "教育学", "教育", "专硕", "学校课程与教学", "045171", 134);
        addGraduate("04", "0451", "教育学", "教育", "专硕", "学生发展与教育", "045172", 135);
        addGraduate("04", "0451", "教育学", "教育", "专硕", "教育领导与管理", "045173", 136);
        addGraduate("04", "0451", "教育学", "教育", "专硕", "汉语国际教育", "045174", 137);
        addGraduate("04", "0452", "教育学", "体育", "学硕", "体育", "0452", 138);
        addGraduate("04", "0452", "教育学", "体育", "学硕", "体育教学", "045201", 139);
        addGraduate("04", "0452", "教育学", "体育", "学硕", "运动训练", "045202", 140);
        addGraduate("04", "0452", "教育学", "体育", "学硕", "竞赛组织", "045203", 141);
        addGraduate("04", "0452", "教育学", "体育", "学硕", "社会体育指导", "045204", 142);
        addGraduate("04", "0453", "教育学", "国际中文教育", "学硕", "国际中文教育", "0453", 143);
        addGraduate("04", "0454", "教育学", "应用心理", "学硕", "应用心理", "0454", 144);
        addGraduate("05", null, "文学", null, "学硕", "文学", "05", 145);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "中国语言文学", "0501", 146);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "文艺学", "050101", 147);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "语言学及应用语言学", "050102", 148);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "汉语言文字学", "050103", 149);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "中国古典文献学", "050104", 150);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "中国古代文学", "050105", 151);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "中国现当代文学", "050106", 152);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "中国少数民族语言文学", "050107", 153);
        addGraduate("05", "0501", "文学", "中国语言文学", "学硕", "比较文学与世界文学", "050108", 154);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "外国语言文学", "0502", 155);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "英语语言文学", "050201", 156);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "俄语语言文学", "050202", 157);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "法语语言文学", "050203", 158);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "德语语言文学", "050204", 159);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "日语语言文学", "050205", 160);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "印度语言文学", "050206", 161);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "西班牙语语言文学", "050207", 162);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "阿拉伯语语言文学", "050208", 163);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "欧洲语言文学", "050209", 164);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "亚非语言文学", "050210", 165);
        addGraduate("05", "0502", "文学", "外国语言文学", "学硕", "外国语言学及应用语言学", "050211", 166);
        addGraduate("05", "0503", "文学", "新闻传播学", "学硕", "新闻传播学", "0503", 167);
        addGraduate("05", "0503", "文学", "新闻传播学", "学硕", "新闻学", "050301", 168);
        addGraduate("05", "0503", "文学", "新闻传播学", "学硕", "传播学", "050302", 169);
        addGraduate("05", "0570", "文学", "区域国别学", "学硕", "区域国别学", "0570", 170);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "翻译", "0551", 171);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "英语笔译", "055101", 172);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "英语口译", "055102", 173);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "俄语笔译", "055103", 174);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "俄语口译", "055104", 175);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "日语笔译", "055105", 176);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "日语口译", "055106", 177);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "法语笔译", "055107", 178);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "法语口译", "055108", 179);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "德语笔译", "055109", 180);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "德语口译", "055110", 181);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "朝鲜语笔译", "055111", 182);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "朝鲜语口译", "055112", 183);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "西班牙语笔译", "055113", 184);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "西班牙语口译", "055114", 185);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "阿拉伯语笔译", "055115", 186);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "阿拉伯语口译", "055116", 187);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "泰语笔译", "055117", 188);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "泰语口译", "055118", 189);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "意大利语笔译", "055119", 190);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "意大利语口译", "055120", 191);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "越南语笔译", "055121", 192);
        addGraduate("05", "0551", "文学", "翻译", "学硕", "越南语口译", "055122", 193);
        addGraduate("05", "0552", "文学", "新闻与传播", "专硕", "新闻与传播", "0552", 194);
        addGraduate("05", "0553", "文学", "出版", "学硕", "出版", "0553", 195);
        addGraduate("06", null, "历史学", null, "学硕", "历史学", "06", 196);
        addGraduate("06", "0601", "历史学", "考古学", "学硕", "考古学", "0601", 197);
        addGraduate("06", "0602", "历史学", "中国史", "学硕", "中国史", "0602", 198);
        addGraduate("06", "0603", "历史学", "世界史", "学硕", "世界史", "0603", 199);
        addGraduate("06", "0670", "历史学", "区域国别学", "学硕", "区域国别学", "0670", 200);
        addGraduate("06", "0651", "历史学", "博物馆", "专硕", "博物馆", "0651", 201);
        addGraduate("07", null, "理学", null, "学硕", "理学", "07", 202);
        addGraduate("07", "0701", "理学", "数学", "学硕", "数学", "0701", 203);
        addGraduate("07", "0701", "理学", "数学", "学硕", "基础数学", "070101", 204);
        addGraduate("07", "0701", "理学", "数学", "学硕", "计算数学", "070102", 205);
        addGraduate("07", "0701", "理学", "数学", "学硕", "概率论与数理统计", "070103", 206);
        addGraduate("07", "0701", "理学", "数学", "学硕", "应用数学", "070104", 207);
        addGraduate("07", "0701", "理学", "数学", "学硕", "运筹学与控制论", "070105", 208);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "物理学", "0702", 209);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "理论物理", "070201", 210);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "粒子物理与原子核物理", "070202", 211);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "原子与分子物理", "070203", 212);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "等离子体物理", "070204", 213);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "凝聚态物理", "070205", 214);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "声学", "070206", 215);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "光学", "070207", 216);
        addGraduate("07", "0702", "理学", "物理学", "学硕", "无线电物理", "070208", 217);
        addGraduate("07", "0703", "理学", "化学", "学硕", "化学", "0703", 218);
        addGraduate("07", "0703", "理学", "化学", "学硕", "无机化学", "070301", 219);
        addGraduate("07", "0703", "理学", "化学", "学硕", "分析化学", "070302", 220);
        addGraduate("07", "0703", "理学", "化学", "学硕", "有机化学", "070303", 221);
        addGraduate("07", "0703", "理学", "化学", "学硕", "物理化学", "070304", 222);
        addGraduate("07", "0703", "理学", "化学", "学硕", "高分子化学与物理", "070305", 223);
        addGraduate("07", "0704", "理学", "天文学", "学硕", "天文学", "0704", 224);
        addGraduate("07", "0704", "理学", "天文学", "学硕", "天体物理", "070401", 225);
        addGraduate("07", "0704", "理学", "天文学", "学硕", "天体测量与天体力学", "070402", 226);
        addGraduate("07", "0705", "理学", "地理学", "学硕", "地理学", "0705", 227);
        addGraduate("07", "0705", "理学", "地理学", "学硕", "自然地理学", "070501", 228);
        addGraduate("07", "0705", "理学", "地理学", "学硕", "人文地理学", "070502", 229);
        addGraduate("07", "0705", "理学", "地理学", "学硕", "地图学与地理信息系统", "070503", 230);
        addGraduate("07", "0706", "理学", "大气科学", "学硕", "大气科学", "0706", 231);
        addGraduate("07", "0706", "理学", "大气科学", "学硕", "气象学", "070601", 232);
        addGraduate("07", "0706", "理学", "大气科学", "学硕", "大气物理学与大气环境", "070602", 233);
        addGraduate("07", "0707", "理学", "海洋科学", "学硕", "海洋科学", "0707", 234);
        addGraduate("07", "0707", "理学", "海洋科学", "学硕", "物理海洋学", "070701", 235);
        addGraduate("07", "0707", "理学", "海洋科学", "学硕", "海洋化学", "070702", 236);
        addGraduate("07", "0707", "理学", "海洋科学", "学硕", "海洋生物学", "070703", 237);
        addGraduate("07", "0707", "理学", "海洋科学", "学硕", "海洋地质", "070704", 238);
        addGraduate("07", "0708", "理学", "地球物理学", "学硕", "地球物理学", "0708", 239);
        addGraduate("07", "0708", "理学", "地球物理学", "学硕", "固体地球物理学", "070801", 240);
        addGraduate("07", "0708", "理学", "地球物理学", "学硕", "空间物理学", "070802", 241);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "地质学", "0709", 242);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "矿物学、岩石学、矿床学", "070901", 243);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "地球化学", "070902", 244);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "古生物学与地层学", "070903", 245);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "构造地质学", "070904", 246);
        addGraduate("07", "0709", "理学", "地质学", "学硕", "第四纪地质学", "070905", 247);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "生物学", "0710", 248);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "植物学", "071001", 249);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "动物学", "071002", 250);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "生理学", "071003", 251);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "水生生物学", "071004", 252);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "微生物学", "071005", 253);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "神经生物学", "071006", 254);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "遗传学", "071007", 255);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "发育生物学", "071008", 256);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "细胞生物学", "071009", 257);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "生物化学与分子生物学", "071010", 258);
        addGraduate("07", "0710", "理学", "生物学", "学硕", "生物物理学", "071011", 259);
        addGraduate("07", "0711", "理学", "系统科学", "学硕", "系统科学", "0711", 260);
        addGraduate("07", "0711", "理学", "系统科学", "学硕", "系统理论", "071101", 261);
        addGraduate("07", "0711", "理学", "系统科学", "学硕", "系统分析与集成", "071102", 262);
        addGraduate("07", "0712", "理学", "科学技术史", "学硕", "科学技术史", "0712", 263);
        addGraduate("07", "0713", "理学", "生态学", "学硕", "生态学", "0713", 264);
        addGraduate("07", "0714", "理学", "统计学", "学硕", "统计学", "0714", 265);
        addGraduate("07", "0770", "理学", "集成电路科学与工程", "学硕", "集成电路科学与工程", "0770", 266);
        addGraduate("07", "0771", "理学", "心理学", "学硕", "心理学", "0771", 267);
        addGraduate("07", "0771", "理学", "心理学", "学硕", "基础心理学", "077101", 268);
        addGraduate("07", "0771", "理学", "心理学", "学硕", "发展与教育心理学", "077102", 269);
        addGraduate("07", "0771", "理学", "心理学", "学硕", "应用心理学", "077103", 270);
        addGraduate("07", "0772", "理学", "力学", "学硕", "力学", "0772", 271);
        addGraduate("07", "0772", "理学", "力学", "学硕", "一般力学与力学基础", "077201", 272);
        addGraduate("07", "0772", "理学", "力学", "学硕", "固体力学", "077202", 273);
        addGraduate("07", "0772", "理学", "力学", "学硕", "流体力学", "077203", 274);
        addGraduate("07", "0772", "理学", "力学", "学硕", "工程力学", "077204", 275);
        addGraduate("07", "0773", "理学", "材料科学与工程", "学硕", "材料科学与工程", "0773", 276);
        addGraduate("07", "0773", "理学", "材料科学与工程", "学硕", "材料物理与化学", "077301", 277);
        addGraduate("07", "0773", "理学", "材料科学与工程", "学硕", "材料学", "077302", 278);
        addGraduate("07", "0773", "理学", "材料科学与工程", "学硕", "材料加工工程", "077303", 279);
        addGraduate("07", "0774", "理学", "电子科学与技术", "学硕", "电子科学与技术", "0774", 280);
        addGraduate("07", "0774", "理学", "电子科学与技术", "学硕", "物理电子学", "077401", 281);
        addGraduate("07", "0774", "理学", "电子科学与技术", "学硕", "电路与系统", "077402", 282);
        addGraduate("07", "0774", "理学", "电子科学与技术", "学硕", "微电子学与固体电子学", "077403", 283);
        addGraduate("07", "0774", "理学", "电子科学与技术", "学硕", "电磁场与微波技术", "077404", 284);
        addGraduate("07", "0775", "理学", "计算机科学与技术", "学硕", "计算机科学与技术", "0775", 285);
        addGraduate("07", "0775", "理学", "计算机科学与技术", "学硕", "计算机系统结构", "077501", 286);
        addGraduate("07", "0775", "理学", "计算机科学与技术", "学硕", "计算机软件与理论", "077502", 287);
        addGraduate("07", "0775", "理学", "计算机科学与技术", "学硕", "计算机应用技术", "077503", 288);
        addGraduate("07", "0776", "理学", "环境科学与工程", "学硕", "环境科学与工程", "0776", 289);
        addGraduate("07", "0776", "理学", "环境科学与工程", "学硕", "环境科学", "077601", 290);
        addGraduate("07", "0776", "理学", "环境科学与工程", "学硕", "环境工程", "077602", 291);
        addGraduate("07", "0777", "理学", "生物医学工程", "学硕", "生物医学工程", "0777", 292);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "基础医学", "0778", 293);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "人体解剖与组织胚胎学", "077801", 294);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "免疫学", "077802", 295);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "病原生物学", "077803", 296);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "病理学与病理生理学", "077804", 297);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "法医学", "077805", 298);
        addGraduate("07", "0778", "理学", "基础医学", "学硕", "放射医学", "077806", 299);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "公共卫生与预防医学", "0779", 300);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "流行病与卫生统计学", "077901", 301);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "劳动卫生与环境卫生学", "077902", 302);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "营养与食品卫生学", "077903", 303);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "儿少卫生与妇幼保健学", "077904", 304);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "卫生毒理学", "077905", 305);
        addGraduate("07", "0779", "理学", "公共卫生与预防医学", "学硕", "军事预防医学", "077906", 306);
        addGraduate("07", "0780", "理学", "药学", "学硕", "药学", "0780", 307);
        addGraduate("07", "0780", "理学", "药学", "学硕", "药物化学", "078001", 308);
        addGraduate("07", "0780", "理学", "药学", "学硕", "药剂学", "078002", 309);
        addGraduate("07", "0780", "理学", "药学", "学硕", "生药学", "078003", 310);
        addGraduate("07", "0780", "理学", "药学", "学硕", "药物分析学", "078004", 311);
        addGraduate("07", "0780", "理学", "药学", "学硕", "微生物与生化药学", "078005", 312);
        addGraduate("07", "0780", "理学", "药学", "学硕", "药理学", "078006", 313);
        addGraduate("07", "0781", "理学", "中药学", "学硕", "中药学", "0781", 314);
        addGraduate("07", "0783", "理学", "护理学", "学硕", "护理学", "0783", 315);
        addGraduate("07", null, "理学", null, "学硕", "教育技术学", "078401", 316);
        addGraduate("07", null, "理学", null, "学硕", "运动人体科学", "078501", 317);
        addGraduate("07", null, "理学", null, "学硕", "农药学", "078601", 318);
        addGraduate("07", "0787", "理学", "遥感科学与技术", "学硕", "遥感科学与技术", "0787", 319);
        addGraduate("07", "0788", "理学", "智能科学与技术", "学硕", "智能科学与技术", "0788", 320);
        addGraduate("07", "0789", "理学", "纳米科学与工程", "学硕", "纳米科学与工程", "0789", 321);
        addGraduate("07", "0751", "理学", "气象", "学硕", "气象", "0751", 322);
        addGraduate("08", null, "工学", null, "学硕", "工学", "08", 323);
        addGraduate("08", "0801", "工学", "力学", "学硕", "力学", "0801", 324);
        addGraduate("08", "0801", "工学", "力学", "学硕", "一般力学与力学基础", "080101", 325);
        addGraduate("08", "0801", "工学", "力学", "学硕", "固体力学", "080102", 326);
        addGraduate("08", "0801", "工学", "力学", "学硕", "流体力学", "080103", 327);
        addGraduate("08", "0801", "工学", "力学", "学硕", "工程力学", "080104", 328);
        addGraduate("08", "0802", "工学", "机械工程", "学硕", "机械工程", "0802", 329);
        addGraduate("08", "0802", "工学", "机械工程", "学硕", "机械制造及其自动化", "080201", 330);
        addGraduate("08", "0802", "工学", "机械工程", "学硕", "机械电子工程", "080202", 331);
        addGraduate("08", "0802", "工学", "机械工程", "学硕", "机械设计及理论", "080203", 332);
        addGraduate("08", "0802", "工学", "机械工程", "学硕", "车辆工程", "080204", 333);
        addGraduate("08", "0803", "工学", "光学工程", "学硕", "光学工程", "0803", 334);
        addGraduate("08", "0804", "工学", "仪器科学与技术", "学硕", "仪器科学与技术", "0804", 335);
        addGraduate("08", "0804", "工学", "仪器科学与技术", "学硕", "精密仪器及机械", "080401", 336);
        addGraduate("08", "0804", "工学", "仪器科学与技术", "学硕", "测试计量技术及仪器", "080402", 337);
        addGraduate("08", "0805", "工学", "材料科学与工程", "学硕", "材料科学与工程", "0805", 338);
        addGraduate("08", "0805", "工学", "材料科学与工程", "学硕", "材料物理与化学", "080501", 339);
        addGraduate("08", "0805", "工学", "材料科学与工程", "学硕", "材料学", "080502", 340);
        addGraduate("08", "0805", "工学", "材料科学与工程", "学硕", "材料加工工程", "080503", 341);
        addGraduate("08", "0806", "工学", "冶金工程", "学硕", "冶金工程", "0806", 342);
        addGraduate("08", "0806", "工学", "冶金工程", "学硕", "冶金物理化学", "080601", 343);
        addGraduate("08", "0806", "工学", "冶金工程", "学硕", "钢铁冶金", "080602", 344);
        addGraduate("08", "0806", "工学", "冶金工程", "学硕", "有色金属冶金", "080603", 345);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "动力工程及工程热物理", "0807", 346);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "工程热物理", "080701", 347);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "热能工程", "080702", 348);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "动力机械及工程", "080703", 349);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "流体机械及工程", "080704", 350);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "制冷及低温工程", "080705", 351);
        addGraduate("08", "0807", "工学", "动力工程及工程热物理", "学硕", "化工过程机械", "080706", 352);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "电气工程", "0808", 353);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "电机与电器", "080801", 354);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "电力系统及其自动化", "080802", 355);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "高电压与绝缘技术", "080803", 356);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "电力电子与电力传动", "080804", 357);
        addGraduate("08", "0808", "工学", "电气工程", "学硕", "电工理论与新技术", "080805", 358);
        addGraduate("08", "0809", "工学", "电子科学与技术", "学硕", "电子科学与技术", "0809", 359);
        addGraduate("08", "0809", "工学", "电子科学与技术", "学硕", "物理电子学", "080901", 360);
        addGraduate("08", "0809", "工学", "电子科学与技术", "学硕", "电路与系统", "080902", 361);
        addGraduate("08", "0809", "工学", "电子科学与技术", "学硕", "微电子学与固体电子学", "080903", 362);
        addGraduate("08", "0809", "工学", "电子科学与技术", "学硕", "电磁场与微波技术", "080904", 363);
        addGraduate("08", "0810", "工学", "信息与通信工程", "学硕", "信息与通信工程", "0810", 364);
        addGraduate("08", "0810", "工学", "信息与通信工程", "学硕", "通信与信息系统", "081001", 365);
        addGraduate("08", "0810", "工学", "信息与通信工程", "学硕", "信号与信息处理", "081002", 366);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "控制科学与工程", "0811", 367);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "控制理论与控制工程", "081101", 368);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "检测技术与自动化装置", "081102", 369);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "系统工程", "081103", 370);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "模式识别与智能系统", "081104", 371);
        addGraduate("08", "0811", "工学", "控制科学与工程", "学硕", "导航、制导与控制", "081105", 372);
        addGraduate("08", "0812", "工学", "计算机科学与技术", "学硕", "计算机科学与技术", "0812", 373);
        addGraduate("08", "0812", "工学", "计算机科学与技术", "学硕", "计算机系统结构", "081201", 374);
        addGraduate("08", "0812", "工学", "计算机科学与技术", "学硕", "计算机软件与理论", "081202", 375);
        addGraduate("08", "0812", "工学", "计算机科学与技术", "学硕", "计算机应用技术", "081203", 376);
        addGraduate("08", "0813", "工学", "建筑学", "学硕", "建筑学", "0813", 377);
        addGraduate("08", "0813", "工学", "建筑学", "学硕", "建筑历史与理论", "081301", 378);
        addGraduate("08", "0813", "工学", "建筑学", "学硕", "建筑设计及其理论", "081302", 379);
        addGraduate("08", "0813", "工学", "建筑学", "学硕", "建筑技术科学", "081304", 380);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "土木工程", "0814", 381);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "岩土工程", "081401", 382);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "结构工程", "081402", 383);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "市政工程", "081403", 384);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "供热、供燃气、通风及空调工程", "081404", 385);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "防灾减灾工程及防护工程", "081405", 386);
        addGraduate("08", "0814", "工学", "土木工程", "学硕", "桥梁与隧道工程", "081406", 387);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "水利工程", "0815", 388);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "水文学及水资源", "081501", 389);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "水力学及河流动力学", "081502", 390);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "水工结构工程", "081503", 391);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "水利水电工程", "081504", 392);
        addGraduate("08", "0815", "工学", "水利工程", "学硕", "港口、海岸及近海工程", "081505", 393);
        addGraduate("08", "0816", "工学", "测绘科学与技术", "学硕", "测绘科学与技术", "0816", 394);
        addGraduate("08", "0816", "工学", "测绘科学与技术", "学硕", "大地测量学与测量工程", "081601", 395);
        addGraduate("08", "0816", "工学", "测绘科学与技术", "学硕", "摄影测量与遥感", "081602", 396);
        addGraduate("08", "0816", "工学", "测绘科学与技术", "学硕", "地图制图学与地理信息工程", "081603", 397);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "化学工程与技术", "0817", 398);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "化学工程", "081701", 399);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "化学工艺", "081702", 400);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "生物化工", "081703", 401);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "应用化学", "081704", 402);
        addGraduate("08", "0817", "工学", "化学工程与技术", "学硕", "工业催化", "081705", 403);
        addGraduate("08", "0818", "工学", "地质资源与地质工程", "学硕", "地质资源与地质工程", "0818", 404);
        addGraduate("08", "0818", "工学", "地质资源与地质工程", "学硕", "矿产普查与勘探", "081801", 405);
        addGraduate("08", "0818", "工学", "地质资源与地质工程", "学硕", "地球探测与信息技术", "081802", 406);
        addGraduate("08", "0818", "工学", "地质资源与地质工程", "学硕", "地质工程", "081803", 407);
        addGraduate("08", "0819", "工学", "矿业工程", "学硕", "矿业工程", "0819", 408);
        addGraduate("08", "0819", "工学", "矿业工程", "学硕", "采矿工程", "081901", 409);
        addGraduate("08", "0819", "工学", "矿业工程", "学硕", "矿物加工工程", "081902", 410);
        addGraduate("08", "0819", "工学", "矿业工程", "学硕", "安全技术及工程", "081903", 411);
        addGraduate("08", "0820", "工学", "石油与天然气工程", "学硕", "石油与天然气工程", "0820", 412);
        addGraduate("08", "0820", "工学", "石油与天然气工程", "学硕", "油气井工程", "082001", 413);
        addGraduate("08", "0820", "工学", "石油与天然气工程", "学硕", "油气田开发工程", "082002", 414);
        addGraduate("08", "0820", "工学", "石油与天然气工程", "学硕", "油气储运工程", "082003", 415);
        addGraduate("08", "0821", "工学", "纺织科学与工程", "学硕", "纺织科学与工程", "0821", 416);
        addGraduate("08", "0821", "工学", "纺织科学与工程", "学硕", "纺织工程", "082101", 417);
        addGraduate("08", "0821", "工学", "纺织科学与工程", "学硕", "纺织材料与纺织品设计", "082102", 418);
        addGraduate("08", "0821", "工学", "纺织科学与工程", "学硕", "纺织化学与染整工程", "082103", 419);
        addGraduate("08", "0821", "工学", "纺织科学与工程", "学硕", "服装设计与工程", "082104", 420);
        addGraduate("08", "0822", "工学", "轻工技术与工程", "学硕", "轻工技术与工程", "0822", 421);
        addGraduate("08", "0822", "工学", "轻工技术与工程", "学硕", "制浆造纸工程", "082201", 422);
        addGraduate("08", "0822", "工学", "轻工技术与工程", "学硕", "制糖工程", "082202", 423);
        addGraduate("08", "0822", "工学", "轻工技术与工程", "学硕", "发酵工程", "082203", 424);
        addGraduate("08", "0822", "工学", "轻工技术与工程", "学硕", "皮革化学与工程", "082204", 425);
        addGraduate("08", "0823", "工学", "交通运输工程", "学硕", "交通运输工程", "0823", 426);
        addGraduate("08", "0823", "工学", "交通运输工程", "学硕", "道路与铁道工程", "082301", 427);
        addGraduate("08", "0823", "工学", "交通运输工程", "学硕", "交通信息工程及控制", "082302", 428);
        addGraduate("08", "0823", "工学", "交通运输工程", "学硕", "交通运输规划与管理", "082303", 429);
        addGraduate("08", "0823", "工学", "交通运输工程", "学硕", "载运工具运用工程", "082304", 430);
        addGraduate("08", "0824", "工学", "船舶与海洋工程", "学硕", "船舶与海洋工程", "0824", 431);
        addGraduate("08", "0824", "工学", "船舶与海洋工程", "学硕", "船舶与海洋结构物设计制造", "082401", 432);
        addGraduate("08", "0824", "工学", "船舶与海洋工程", "学硕", "轮机工程", "082402", 433);
        addGraduate("08", "0824", "工学", "船舶与海洋工程", "学硕", "水声工程", "082403", 434);
        addGraduate("08", "0825", "工学", "航空宇航科学与技术", "学硕", "航空宇航科学与技术", "0825", 435);
        addGraduate("08", "0825", "工学", "航空宇航科学与技术", "学硕", "飞行器设计", "082501", 436);
        addGraduate("08", "0825", "工学", "航空宇航科学与技术", "学硕", "航空宇航推进理论与工程", "082502", 437);
        addGraduate("08", "0825", "工学", "航空宇航科学与技术", "学硕", "航空宇航制造工程", "082503", 438);
        addGraduate("08", "0825", "工学", "航空宇航科学与技术", "学硕", "人机与环境工程", "082504", 439);
        addGraduate("08", "0826", "工学", "兵器科学与技术", "学硕", "兵器科学与技术", "0826", 440);
        addGraduate("08", "0826", "工学", "兵器科学与技术", "学硕", "武器系统与运用工程", "082601", 441);
        addGraduate("08", "0826", "工学", "兵器科学与技术", "学硕", "兵器发射理论与技术", "082602", 442);
        addGraduate("08", "0826", "工学", "兵器科学与技术", "学硕", "火炮、自动武器与弹药工程", "082603", 443);
        addGraduate("08", "0826", "工学", "兵器科学与技术", "学硕", "军事化学与烟火技术", "082604", 444);
        addGraduate("08", "0827", "工学", "核科学与技术", "学硕", "核科学与技术", "0827", 445);
        addGraduate("08", "0827", "工学", "核科学与技术", "学硕", "核能科学与工程", "082701", 446);
        addGraduate("08", "0827", "工学", "核科学与技术", "学硕", "核燃料循环与材料", "082702", 447);
        addGraduate("08", "0827", "工学", "核科学与技术", "学硕", "核技术及应用", "082703", 448);
        addGraduate("08", "0827", "工学", "核科学与技术", "学硕", "辐射防护及环境保护", "082704", 449);
        addGraduate("08", "0828", "工学", "农业工程", "学硕", "农业工程", "0828", 450);
        addGraduate("08", "0828", "工学", "农业工程", "学硕", "农业机械化工程", "082801", 451);
        addGraduate("08", "0828", "工学", "农业工程", "学硕", "农业水土工程", "082802", 452);
        addGraduate("08", "0828", "工学", "农业工程", "学硕", "农业生物环境与能源工程", "082803", 453);
        addGraduate("08", "0828", "工学", "农业工程", "学硕", "农业电气化与自动化", "082804", 454);
        addGraduate("08", "0829", "工学", "林业工程", "学硕", "林业工程", "0829", 455);
        addGraduate("08", "0829", "工学", "林业工程", "学硕", "森林工程", "082901", 456);
        addGraduate("08", "0829", "工学", "林业工程", "学硕", "木材科学与技术", "082902", 457);
        addGraduate("08", "0829", "工学", "林业工程", "学硕", "林产化学加工工程", "082903", 458);
        addGraduate("08", "0830", "工学", "环境科学与工程", "学硕", "环境科学与工程", "0830", 459);
        addGraduate("08", "0830", "工学", "环境科学与工程", "学硕", "环境科学", "083001", 460);
        addGraduate("08", "0830", "工学", "环境科学与工程", "学硕", "环境工程", "083002", 461);
        addGraduate("08", "0831", "工学", "生物医学工程", "学硕", "生物医学工程", "0831", 462);
        addGraduate("08", "0832", "工学", "食品科学与工程", "学硕", "食品科学与工程", "0832", 463);
        addGraduate("08", "0832", "工学", "食品科学与工程", "学硕", "食品科学", "083201", 464);
        addGraduate("08", "0832", "工学", "食品科学与工程", "学硕", "粮食、油脂及植物蛋白工程", "083202", 465);
        addGraduate("08", "0832", "工学", "食品科学与工程", "学硕", "农产品加工及贮藏工程", "083203", 466);
        addGraduate("08", "0832", "工学", "食品科学与工程", "学硕", "水产品加工及贮藏工程", "083204", 467);
        addGraduate("08", "0833", "工学", "城乡规划学", "学硕", "城乡规划学", "0833", 468);
        addGraduate("08", "0835", "工学", "软件工程", "学硕", "软件工程", "0835", 469);
        addGraduate("08", "0836", "工学", "生物工程", "学硕", "生物工程", "0836", 470);
        addGraduate("08", "0837", "工学", "安全科学与工程", "学硕", "安全科学与工程", "0837", 471);
        addGraduate("08", "0838", "工学", "公安技术", "学硕", "公安技术", "0838", 472);
        addGraduate("08", "0839", "工学", "网络空间安全", "学硕", "网络空间安全", "0839", 473);
        addGraduate("08", "0870", "工学", "科学技术史", "学硕", "科学技术史", "0870", 474);
        addGraduate("08", "0871", "工学", "管理科学与工程", "学硕", "管理科学与工程", "0871", 475);
        addGraduate("08", "0872", "工学", "设计学", "学硕", "设计学", "0872", 476);
        addGraduate("08", "0873", "工学", "集成电路科学与工程", "学硕", "集成电路科学与工程", "0873", 477);
        addGraduate("08", "0874", "工学", "国家安全学", "学硕", "国家安全学", "0874", 478);
        addGraduate("08", "0875", "工学", "遥感科学与技术", "学硕", "遥感科学与技术", "0875", 479);
        addGraduate("08", "0876", "工学", "智能科学与技术", "学硕", "智能科学与技术", "0876", 480);
        addGraduate("08", "0877", "工学", "纳米科学与工程", "学硕", "纳米科学与工程", "0877", 481);
        addGraduate("08", "0851", "工学", "建筑", "专硕", "建筑", "0851", 482);
        addGraduate("08", "0853", "工学", "城乡规划", "专硕", "城乡规划", "0853", 483);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "电子信息", "0854", 484);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "新一代电子信息技术(含量子技术等)", "085401", 485);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "通信工程(含宽带网络、移动通信等)", "085402", 486);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "集成电路工程", "085403", 487);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "计算机技术", "085404", 488);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "软件工程", "085405", 489);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "控制工程", "085406", 490);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "仪器仪表工程", "085407", 491);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "光电信息工程", "085408", 492);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "生物医学工程", "085409", 493);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "人工智能", "085410", 494);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "大数据技术与工程", "085411", 495);
        addGraduate("08", "0854", "工学", "电子信息", "学硕", "网络与信息安全", "085412", 496);
        addGraduate("08", "0855", "工学", "机械", "学硕", "机械", "0855", 497);
        addGraduate("08", "0855", "工学", "机械", "学硕", "机械工程", "085501", 498);
        addGraduate("08", "0855", "工学", "机械", "学硕", "车辆工程", "085502", 499);
        addGraduate("08", "0855", "工学", "机械", "学硕", "航空工程", "085503", 500);
        addGraduate("08", "0855", "工学", "机械", "学硕", "航天工程", "085504", 501);
        addGraduate("08", "0855", "工学", "机械", "学硕", "船舶工程", "085505", 502);
        addGraduate("08", "0855", "工学", "机械", "学硕", "兵器工程", "085506", 503);
        addGraduate("08", "0855", "工学", "机械", "学硕", "工业设计工程", "085507", 504);
        addGraduate("08", "0855", "工学", "机械", "学硕", "农机装备工程", "085508", 505);
        addGraduate("08", "0855", "工学", "机械", "学硕", "智能制造技术", "085509", 506);
        addGraduate("08", "0855", "工学", "机械", "学硕", "机器人工程", "085510", 507);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "材料与化工", "0856", 508);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "材料工程", "085601", 509);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "化学工程", "085602", 510);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "冶金工程", "085603", 511);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "纺织工程", "085604", 512);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "林业工程", "085605", 513);
        addGraduate("08", "0856", "工学", "材料与化工", "学硕", "轻化工程(含皮革、纸张、织物加工等)", "085606", 514);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "资源与环境", "0857", 515);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "环境工程", "085701", 516);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "安全工程", "085702", 517);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "地质工程", "085703", 518);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "测绘工程", "085704", 519);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "矿业工程", "085705", 520);
        addGraduate("08", "0857", "工学", "资源与环境", "学硕", "石油与天然气工程", "085706", 521);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "能源动力", "0858", 522);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "电气工程", "085801", 523);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "动力工程", "085802", 524);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "核能工程", "085803", 525);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "航空发动机工程", "085804", 526);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "燃气轮机工程", "085805", 527);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "航天动力工程", "085806", 528);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "清洁能源技术", "085807", 529);
        addGraduate("08", "0858", "工学", "能源动力", "学硕", "储能技术", "085808", 530);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "土木水利", "0859", 531);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "土木工程", "085901", 532);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "水利工程", "085902", 533);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "海洋工程", "085903", 534);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "农田水土工程", "085904", 535);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "市政工程(含给排水等)", "085905", 536);
        addGraduate("08", "0859", "工学", "土木水利", "学硕", "人工环境工程(含供热、通风及空调等)", "085906", 537);
        addGraduate("08", "0860", "工学", "生物与医药", "学硕", "生物与医药", "0860", 538);
        addGraduate("08", "0860", "工学", "生物与医药", "学硕", "生物技术与工程", "086001", 539);
        addGraduate("08", "0860", "工学", "生物与医药", "学硕", "制药工程", "086002", 540);
        addGraduate("08", "0860", "工学", "生物与医药", "学硕", "食品工程", "086003", 541);
        addGraduate("08", "0860", "工学", "生物与医药", "学硕", "发酵工程", "086004", 542);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "交通运输", "0861", 543);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "轨道交通运输", "086101", 544);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "道路交通运输", "086102", 545);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "水路交通运输", "086103", 546);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "航空交通运输", "086104", 547);
        addGraduate("08", "0861", "工学", "交通运输", "学硕", "管道交通运输", "086105", 548);
        addGraduate("08", "0862", "工学", "风景园林", "学硕", "风景园林", "0862", 549);
        addGraduate("09", null, "农学", null, "学硕", "农学", "09", 550);
        addGraduate("09", "0901", "农学", "作物学", "学硕", "作物学", "0901", 551);
        addGraduate("09", "0901", "农学", "作物学", "学硕", "作物栽培学与耕作学", "090101", 552);
        addGraduate("09", "0901", "农学", "作物学", "学硕", "作物遗传育种", "090102", 553);
        addGraduate("09", "0902", "农学", "园艺学", "学硕", "园艺学", "0902", 554);
        addGraduate("09", "0902", "农学", "园艺学", "学硕", "果树学", "090201", 555);
        addGraduate("09", "0902", "农学", "园艺学", "学硕", "蔬菜学", "090202", 556);
        addGraduate("09", "0902", "农学", "园艺学", "学硕", "茶学", "090203", 557);
        addGraduate("09", "0903", "农学", "农业资源与环境", "学硕", "农业资源与环境", "0903", 558);
        addGraduate("09", "0903", "农学", "农业资源与环境", "学硕", "土壤学", "090301", 559);
        addGraduate("09", "0903", "农学", "农业资源与环境", "学硕", "植物营养学", "090302", 560);
        addGraduate("09", "0904", "农学", "植物保护", "学硕", "植物保护", "0904", 561);
        addGraduate("09", "0904", "农学", "植物保护", "学硕", "植物病理学", "090401", 562);
        addGraduate("09", "0904", "农学", "植物保护", "学硕", "农业昆虫与害虫防治", "090402", 563);
        addGraduate("09", "0904", "农学", "植物保护", "学硕", "农药学", "090403", 564);
        addGraduate("09", "0905", "农学", "畜牧学", "学硕", "畜牧学", "0905", 565);
        addGraduate("09", "0905", "农学", "畜牧学", "学硕", "动物遗传育种与繁殖", "090501", 566);
        addGraduate("09", "0905", "农学", "畜牧学", "学硕", "动物营养与饲料科学", "090502", 567);
        addGraduate("09", "0905", "农学", "畜牧学", "学硕", "特种经济动物饲养", "090504", 568);
        addGraduate("09", "0906", "农学", "兽医学", "学硕", "兽医学", "0906", 569);
        addGraduate("09", "0906", "农学", "兽医学", "学硕", "基础兽医学", "090601", 570);
        addGraduate("09", "0906", "农学", "兽医学", "学硕", "预防兽医学", "090602", 571);
        addGraduate("09", "0906", "农学", "兽医学", "学硕", "临床兽医学", "090603", 572);
        addGraduate("09", "0907", "农学", "林学", "学硕", "林学", "0907", 573);
        addGraduate("09", "0907", "农学", "林学", "学硕", "林木遗传育种", "090701", 574);
        addGraduate("09", "0907", "农学", "林学", "学硕", "森林培育", "090702", 575);
        addGraduate("09", "0907", "农学", "林学", "学硕", "森林保护学", "090703", 576);
        addGraduate("09", "0907", "农学", "林学", "学硕", "森林经理学", "090704", 577);
        addGraduate("09", "0907", "农学", "林学", "学硕", "野生动植物保护与利用", "090705", 578);
        addGraduate("09", "0907", "农学", "林学", "学硕", "园林植物与观赏园艺", "090706", 579);
        addGraduate("09", "0907", "农学", "林学", "学硕", "水土保持与荒漠化防治", "090707", 580);
        addGraduate("09", "0908", "农学", "水产", "学硕", "水产", "0908", 581);
        addGraduate("09", "0908", "农学", "水产", "学硕", "水产养殖", "090801", 582);
        addGraduate("09", "0908", "农学", "水产", "学硕", "捕捞学", "090802", 583);
        addGraduate("09", "0908", "农学", "水产", "学硕", "渔业资源", "090803", 584);
        addGraduate("09", "0909", "农学", "草学", "学硕", "草学", "0909", 585);
        addGraduate("09", "0910", "农学", "水土保持与荒漠化防治学", "学硕", "水土保持与荒漠化防治学", "0910", 586);
        addGraduate("09", "0970", "农学", "科学技术史", "学硕", "科学技术史", "0970", 587);
        addGraduate("09", "0971", "农学", "环境科学与工程", "学硕", "环境科学与工程", "0971", 588);
        addGraduate("09", "0971", "农学", "环境科学与工程", "学硕", "环境科学", "097101", 589);
        addGraduate("09", "0971", "农学", "环境科学与工程", "学硕", "环境工程", "097102", 590);
        addGraduate("09", "0972", "农学", "食品科学与工程", "学硕", "食品科学与工程", "0972", 591);
        addGraduate("09", "0972", "农学", "食品科学与工程", "学硕", "食品科学", "097201", 592);
        addGraduate("09", "0972", "农学", "食品科学与工程", "学硕", "粮食、油脂及植物蛋白工程", "097202", 593);
        addGraduate("09", "0972", "农学", "食品科学与工程", "学硕", "农产品加工及贮藏工程", "097203", 594);
        addGraduate("09", "0972", "农学", "食品科学与工程", "学硕", "水产品加工及贮藏工程", "097204", 595);
        addGraduate("09", "0951", "农学", "农业", "学硕", "农业", "0951", 596);
        addGraduate("09", "0951", "农学", "农业", "学硕", "农艺与种业", "095131", 597);
        addGraduate("09", "0951", "农学", "农业", "学硕", "资源利用与植物保护", "095132", 598);
        addGraduate("09", "0951", "农学", "农业", "学硕", "畜牧", "095133", 599);
        addGraduate("09", "0951", "农学", "农业", "学硕", "渔业发展", "095134", 600);
        addGraduate("09", "0951", "农学", "农业", "学硕", "食品加工与安全", "095135", 601);
        addGraduate("09", "0951", "农学", "农业", "学硕", "农业工程与信息技术", "095136", 602);
        addGraduate("09", "0951", "农学", "农业", "学硕", "农业管理", "095137", 603);
        addGraduate("09", "0951", "农学", "农业", "学硕", "农村发展", "095138", 604);
        addGraduate("09", "0952", "农学", "兽医", "学硕", "兽医", "0952", 605);
        addGraduate("09", "0954", "农学", "林业", "学硕", "林业", "0954", 606);
        addGraduate("09", "0955", "农学", "食品与营养", "专硕", "食品与营养", "0955", 607);
        addGraduate("10", null, "医学", null, "学硕", "医学", "10", 608);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "基础医学", "1001", 609);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "人体解剖与组织胚胎学", "100101", 610);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "免疫学", "100102", 611);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "病原生物学", "100103", 612);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "病理学与病理生理学", "100104", 613);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "法医学", "100105", 614);
        addGraduate("10", "1001", "医学", "基础医学", "学硕", "放射医学", "100106", 615);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "临床医学", "1002", 616);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "内科学", "100201", 617);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "儿科学", "100202", 618);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "老年医学", "100203", 619);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "神经病学", "100204", 620);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "精神病与精神卫生学", "100205", 621);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "皮肤病与性病学", "100206", 622);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "影像医学与核医学", "100207", 623);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "临床检验诊断学", "100208", 624);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "外科学", "100210", 625);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "妇产科学", "100211", 626);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "眼科学", "100212", 627);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "耳鼻咽喉科学", "100213", 628);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "肿瘤学", "100214", 629);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "康复医学与理疗学", "100215", 630);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "运动医学", "100216", 631);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "麻醉学", "100217", 632);
        addGraduate("10", "1002", "医学", "临床医学", "学硕", "急诊医学", "100218", 633);
        addGraduate("10", "1003", "医学", "口腔医学", "学硕", "口腔医学", "1003", 634);
        addGraduate("10", "1003", "医学", "口腔医学", "学硕", "口腔基础医学", "100301", 635);
        addGraduate("10", "1003", "医学", "口腔医学", "学硕", "口腔临床医学", "100302", 636);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "公共卫生与预防医学", "1004", 637);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "流行病与卫生统计学", "100401", 638);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "劳动卫生与环境卫生学", "100402", 639);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "营养与食品卫生学", "100403", 640);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "儿少卫生与妇幼保健学", "100404", 641);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "卫生毒理学", "100405", 642);
        addGraduate("10", "1004", "医学", "公共卫生与预防医学", "学硕", "军事预防医学", "100406", 643);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医学", "1005", 644);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医基础理论", "100501", 645);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医临床基础", "100502", 646);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医医史文献", "100503", 647);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "方剂学", "100504", 648);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医诊断学", "100505", 649);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医内科学", "100506", 650);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医外科学", "100507", 651);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医骨伤科学", "100508", 652);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医妇科学", "100509", 653);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医儿科学", "100510", 654);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "中医五官科学", "100511", 655);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "针灸推拿学", "100512", 656);
        addGraduate("10", "1005", "医学", "中医学", "学硕", "民族医学(含： 藏医学、蒙医学等)", "100513", 657);
        addGraduate("10", "1006", "医学", "中西医结合", "学硕", "中西医结合", "1006", 658);
        addGraduate("10", "1006", "医学", "中西医结合", "学硕", "中西医结合基础", "100601", 659);
        addGraduate("10", "1006", "医学", "中西医结合", "学硕", "中西医结合临床", "100602", 660);
        addGraduate("10", "1007", "医学", "药学", "学硕", "药学", "1007", 661);
        addGraduate("10", "1007", "医学", "药学", "学硕", "药物化学", "100701", 662);
        addGraduate("10", "1007", "医学", "药学", "学硕", "药剂学", "100702", 663);
        addGraduate("10", "1007", "医学", "药学", "学硕", "生药学", "100703", 664);
        addGraduate("10", "1007", "医学", "药学", "学硕", "药物分析学", "100704", 665);
        addGraduate("10", "1007", "医学", "药学", "学硕", "微生物与生化药学", "100705", 666);
        addGraduate("10", "1007", "医学", "药学", "学硕", "药理学", "100706", 667);
        addGraduate("10", "1008", "医学", "中药学", "学硕", "中药学", "1008", 668);
        addGraduate("10", "1009", "医学", "特种医学", "学硕", "特种医学", "1009", 669);
        addGraduate("10", "1011", "医学", "护理学", "学硕", "护理学", "1011", 670);
        addGraduate("10", "1012", "医学", "法医学", "学硕", "法医学", "1012", 671);
        addGraduate("10", "1071", "医学", "科学技术史", "学硕", "科学技术史", "1071", 672);
        addGraduate("10", "1072", "医学", "生物医学工程", "学硕", "生物医学工程", "1072", 673);
        addGraduate("10", null, "医学", null, "学硕", "运动人体科学", "107301", 674);
        addGraduate("10", null, "医学", null, "学硕", "社会医学与卫生事业管理", "107401", 675);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "临床医学", "1051", 676);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "内科学", "105101", 677);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "儿科学", "105102", 678);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "老年医学", "105103", 679);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "神经病学", "105104", 680);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "精神病与精神卫生学", "105105", 681);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "皮肤病与性病学", "105106", 682);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "急诊医学", "105107", 683);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "重症医学", "105108", 684);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "全科医学", "105109", 685);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "康复医学与理疗学", "105110", 686);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "外科学", "105111", 687);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "儿外科学", "105112", 688);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "骨科学", "105113", 689);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "运动医学", "105114", 690);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "妇产科学", "105115", 691);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "眼科学", "105116", 692);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "耳鼻咽喉科学", "105117", 693);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "麻醉学", "105118", 694);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "临床病理", "105119", 695);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "临床检验诊断学", "105120", 696);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "肿瘤学", "105121", 697);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "放射肿瘤学", "105122", 698);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "放射影像学", "105123", 699);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "超声医学", "105124", 700);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "核医学", "105125", 701);
        addGraduate("10", "1051", "医学", "临床医学", "学硕", "医学遗传学", "105126", 702);
        addGraduate("10", "1052", "医学", "口腔医学", "学硕", "口腔医学", "1052", 703);
        addGraduate("10", "1053", "医学", "公共卫生", "学硕", "公共卫生", "1053", 704);
        addGraduate("10", "1054", "医学", "护理", "专硕", "护理", "1054", 705);
        addGraduate("10", "1055", "医学", "药学", "学硕", "药学", "1055", 706);
        addGraduate("10", "1056", "医学", "中药", "专硕", "中药", "1056", 707);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医", "1057", 708);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医内科学", "105701", 709);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医外科学", "105702", 710);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医骨伤科学", "105703", 711);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医妇科学", "105704", 712);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医儿科学", "105705", 713);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中医五官科学", "105706", 714);
        addGraduate("10", "1057", "医学", "中医", "学硕", "针灸推拿学", "105707", 715);
        addGraduate("10", "1057", "医学", "中医", "学硕", "民族医学(含： 藏医学、蒙医学等)", "105708", 716);
        addGraduate("10", "1057", "医学", "中医", "学硕", "中西医结合临床", "105709", 717);
        addGraduate("10", "1057", "医学", "中医", "学硕", "全科医学(中医，不授博士学位)", "105710", 718);
        addGraduate("10", "1058", "医学", "医学技术", "学硕", "医学技术", "1058", 719);
        addGraduate("10", "1059", "医学", "针灸", "专硕", "针灸", "1059", 720);
        addGraduate("11", null, "军事学", null, "学硕", "军事学", "11", 721);
        addGraduate("11", "1101", "军事学", "军事思想与军事历史", "学硕", "军事思想与军事历史", "1101", 722);
        addGraduate("11", "1102", "军事学", "战略学", "学硕", "战略学", "1102", 723);
        addGraduate("11", "1103", "军事学", "联合作战学", "学硕", "联合作战学", "1103", 724);
        addGraduate("11", "1104", "军事学", "军兵种作战学", "学硕", "军兵种作战学", "1104", 725);
        addGraduate("11", "1105", "军事学", "军队指挥学", "学硕", "军队指挥学", "1105", 726);
        addGraduate("11", "1106", "军事学", "军队政治工作学", "学硕", "军队政治工作学", "1106", 727);
        addGraduate("11", "1107", "军事学", "军事后勤学", "学硕", "军事后勤学", "1107", 728);
        addGraduate("11", "1108", "军事学", "军事装备学", "学硕", "军事装备学", "1108", 729);
        addGraduate("11", "1109", "军事学", "军事管理学", "学硕", "军事管理学", "1109", 730);
        addGraduate("11", "1110", "军事学", "军事训练学", "学硕", "军事训练学", "1110", 731);
        addGraduate("11", "1111", "军事学", "军事智能", "学硕", "军事智能", "1111", 732);
        addGraduate("11", "1170", "军事学", "国家安全学", "学硕", "国家安全学", "1170", 733);
        addGraduate("11", "1152", "军事学", "联合作战指挥", "专硕", "联合作战指挥", "1152", 734);
        addGraduate("11", "1153", "军事学", "军兵种作战指挥", "专硕", "军兵种作战指挥", "1153", 735);
        addGraduate("11", "1154", "军事学", "作战指挥保障", "专硕", "作战指挥保障", "1154", 736);
        addGraduate("11", "1155", "军事学", "战时政治工作", "专硕", "战时政治工作", "1155", 737);
        addGraduate("11", "1156", "军事学", "后勤与装备保障", "专硕", "后勤与装备保障", "1156", 738);
        addGraduate("11", "1157", "军事学", "军事训练与管理", "专硕", "军事训练与管理", "1157", 739);
        addGraduate("12", null, "管理学", null, "学硕", "管理学", "12", 740);
        addGraduate("12", "1201", "管理学", "管理科学与工程", "学硕", "管理科学与工程", "1201", 741);
        addGraduate("12", "1202", "管理学", "工商管理学", "学硕", "工商管理学", "1202", 742);
        addGraduate("12", "1202", "管理学", "工商管理学", "学硕", "会计学", "120201", 743);
        addGraduate("12", "1202", "管理学", "工商管理学", "学硕", "企业管理", "120202", 744);
        addGraduate("12", "1202", "管理学", "工商管理学", "学硕", "旅游管理", "120203", 745);
        addGraduate("12", "1202", "管理学", "工商管理学", "学硕", "技术经济及管理", "120204", 746);
        addGraduate("12", "1203", "管理学", "农林经济管理", "学硕", "农林经济管理", "1203", 747);
        addGraduate("12", "1203", "管理学", "农林经济管理", "学硕", "农业经济管理", "120301", 748);
        addGraduate("12", "1203", "管理学", "农林经济管理", "学硕", "林业经济管理", "120302", 749);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "公共管理学", "1204", 750);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "行政管理", "120401", 751);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "社会医学与卫生事业管理", "120402", 752);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "教育经济与管理", "120403", 753);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "社会保障", "120404", 754);
        addGraduate("12", "1204", "管理学", "公共管理学", "学硕", "土地资源管理", "120405", 755);
        addGraduate("12", "1205", "管理学", "信息资源管理", "学硕", "信息资源管理", "1205", 756);
        addGraduate("12", "1205", "管理学", "信息资源管理", "学硕", "图书馆学", "120501", 757);
        addGraduate("12", "1205", "管理学", "信息资源管理", "学硕", "情报学", "120502", 758);
        addGraduate("12", "1205", "管理学", "信息资源管理", "学硕", "档案学", "120503", 759);
        addGraduate("12", "1270", "管理学", "安全科学与工程", "学硕", "安全科学与工程", "1270", 760);
        addGraduate("12", "1271", "管理学", "国家安全学", "学硕", "国家安全学", "1271", 761);
        addGraduate("12", "1251", "管理学", "工商管理", "专硕", "工商管理", "1251", 762);
        addGraduate("12", "1252", "管理学", "公共管理", "专硕", "公共管理", "1252", 763);
        addGraduate("12", "1253", "管理学", "会计", "学硕", "会计", "1253", 764);
        addGraduate("12", "1254", "管理学", "旅游管理", "专硕", "旅游管理", "1254", 765);
        addGraduate("12", "1255", "管理学", "图书情报", "专硕", "图书情报", "1255", 766);
        addGraduate("12", "1256", "管理学", "工程管理", "专硕", "工程管理", "1256", 767);
        addGraduate("12", "1256", "管理学", "工程管理", "专硕", "工程管理", "125601", 768);
        addGraduate("12", "1256", "管理学", "工程管理", "专硕", "项目管理", "125602", 769);
        addGraduate("12", "1256", "管理学", "工程管理", "专硕", "工业工程与管理", "125603", 770);
        addGraduate("12", "1256", "管理学", "工程管理", "专硕", "物流工程与管理", "125604", 771);
        addGraduate("12", "1257", "管理学", "审计", "学硕", "审计", "1257", 772);
        addGraduate("13", null, "艺术学", null, "学硕", "艺术学", "13", 773);
        addGraduate("13", "1301", "艺术学", "艺术学", "学硕", "艺术学", "1301", 774);
        addGraduate("13", "1370", "艺术学", "设计学", "学硕", "设计学", "1370", 775);
        addGraduate("13", "1352", "艺术学", "音乐", "学硕", "音乐", "1352", 776);
        addGraduate("13", "1353", "艺术学", "舞蹈", "学硕", "舞蹈", "1353", 777);
        addGraduate("13", "1354", "艺术学", "戏剧与影视", "学硕", "戏剧与影视", "1354", 778);
        addGraduate("13", "1355", "艺术学", "戏曲与曲艺", "学硕", "戏曲与曲艺", "1355", 779);
        addGraduate("13", "1356", "艺术学", "美术与书法", "学硕", "美术与书法", "1356", 780);
        addGraduate("13", "1357", "艺术学", "设计", "学硕", "设计", "1357", 781);
        addGraduate("14", null, "交叉学科", null, "学硕", "交叉学科", "14", 782);
        addGraduate("14", "1401", "交叉学科", "集成电路科学与工程", "学硕", "集成电路科学与工程", "1401", 783);
        addGraduate("14", "1402", "交叉学科", "国家安全学", "学硕", "国家安全学", "1402", 784);
        addGraduate("14", "1403", "交叉学科", "设计学", "学硕", "设计学", "1403", 785);
        addGraduate("14", "1404", "交叉学科", "遥感科学与技术", "学硕", "遥感科学与技术", "1404", 786);
        addGraduate("14", "1405", "交叉学科", "智能科学与技术", "学硕", "智能科学与技术", "1405", 787);
        addGraduate("14", "1406", "交叉学科", "纳米科学与工程", "学硕", "纳米科学与工程", "1406", 788);
        addGraduate("14", "1407", "交叉学科", "区域国别学", "学硕", "区域国别学", "1407", 789);
        addGraduate("14", "1451", "交叉学科", "文物", "学硕", "文物", "1451", 790);
        addGraduate("14", "1452", "交叉学科", "密码", "专硕", "密码", "1452", 791);
    }
}
