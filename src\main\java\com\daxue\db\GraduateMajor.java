package com.daxue.db;

import java.util.List;
import java.util.ArrayList;

/**
 * 研究生专业实体类
 * 对应研究生专业目录数据结构
 */
public class GraduateMajor {
    
    /** 学科门类代码 (如: 01, 02, 03...) */
    private String categoryOneCode;
    
    /** 一级学科代码 (如: 0101, 0201...) */
    private String categoryTwoCode;
    
    /** 学科门类名称 (如: 哲学, 经济学, 法学...) */
    private String categoryOne;
    
    /** 一级学科名称 (如: 哲学, 理论经济学...) */
    private String categoryTwo;
    
    /** 学位类型 (学硕, 专硕) */
    private String type;
    
    /** 专业名称 */
    private String majorName;
    
    /** 专业代码 */
    private String majorCode;
    
    /** 排序号 */
    private int sort;
    
    public GraduateMajor() {}
    
    public GraduateMajor(String categoryOneCode, String categoryTwoCode, String categoryOne, 
                        String categoryTwo, String type, String majorName, String majorCode, int sort) {
        this.categoryOneCode = categoryOneCode;
        this.categoryTwoCode = categoryTwoCode;
        this.categoryOne = categoryOne;
        this.categoryTwo = categoryTwo;
        this.type = type;
        this.majorName = majorName;
        this.majorCode = majorCode;
        this.sort = sort;
    }
    
    // Getters and Setters
    public String getCategoryOneCode() {
        return categoryOneCode;
    }
    
    public void setCategoryOneCode(String categoryOneCode) {
        this.categoryOneCode = categoryOneCode;
    }
    
    public String getCategoryTwoCode() {
        return categoryTwoCode;
    }
    
    public void setCategoryTwoCode(String categoryTwoCode) {
        this.categoryTwoCode = categoryTwoCode;
    }
    
    public String getCategoryOne() {
        return categoryOne;
    }
    
    public void setCategoryOne(String categoryOne) {
        this.categoryOne = categoryOne;
    }
    
    public String getCategoryTwo() {
        return categoryTwo;
    }
    
    public void setCategoryTwo(String categoryTwo) {
        this.categoryTwo = categoryTwo;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getMajorName() {
        return majorName;
    }
    
    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }
    
    public String getMajorCode() {
        return majorCode;
    }
    
    public void setMajorCode(String majorCode) {
        this.majorCode = majorCode;
    }
    
    public int getSort() {
        return sort;
    }
    
    public void setSort(int sort) {
        this.sort = sort;
    }
    
    /**
     * 获取专业名称的核心关键词
     * 去除常见的修饰词，提取核心专业词汇
     */
    public String getCoreKeywords() {
        if (majorName == null) return "";
        
        // 使用更智能的关键词提取方法
        List<String> keywords = extractKeywordsFromMajorName(majorName);
        return String.join(",", keywords);
    }
    
    /**
     * 从专业名称中提取关键词
     * 保留有意义的词汇，去除修饰词
     */
    private List<String> extractKeywordsFromMajorName(String name) {
        List<String> keywords = new ArrayList<>();
        
        // 先进行基本的词汇分割和清理
        String cleanName = name.replaceAll("[()（）\\[\\]【】<>《》、，。；：！？\\s]+", "");
        
        // 定义修饰词列表（研究生专业特有的修饰词）
        String[] modifiers = {"理论", "基础", "应用", "现代", "高等", "专业", "系统", "研究", "发展", "学科"};
        
        // 定义核心学科词汇模式（研究生专业更细分）
        String[] patterns = {
            "计算机.*?(?=科学|技术|应用|系统|软件|$)",
            "电子.*?(?=科学|技术|信息|$)", 
            "机械.*?(?=工程|制造|设计|电子|$)",
            "材料.*?(?=科学|工程|物理|化学|$)",
            "化学.*?(?=工程|工艺|$)",
            "土木.*?(?=工程|$)",
            "软件.*?(?=工程|$)",
            "信息.*?(?=与通信|处理|安全|管理|$)",
            "通信.*?(?=与信息|工程|$)",
            "控制.*?(?=科学|理论|$)",
            "管理.*?(?=科学|学|$)",
            "临床.*?(?=医学|$)",
            "基础.*?(?=医学|数学|物理|$)",
            "马克思主义.*?(?=理论|哲学|$)",
            "中国.*?(?=语言|文学|史|哲学|$)",
            "外国.*?(?=语言|文学|$)",
            "新闻.*?(?=传播|学|$)",
            "人工智能",
            "大数据",
            "物联网",
            "区块链",
            "生物医学",
            "纳米科学"
        };
        
        // 使用正则表达式提取核心词汇
        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(cleanName);
            while (m.find()) {
                String match = m.group();
                if (!match.isEmpty() && match.length() > 1) {
                    keywords.add(match);
                }
            }
        }
        
        // 如果没有匹配到模式，使用传统方法
        if (keywords.isEmpty()) {
            String core = cleanName;
            // 去除修饰词
            for (String modifier : modifiers) {
                core = core.replace(modifier, "");
            }
            
            // 去除常见后缀，但保留有意义的部分
            core = core.replace("与", "")
                      .replace("及", "");
            
            // 分割成有意义的词汇片段
            if (core.length() > 0) {
                // 简单的中文词汇分割
                List<String> segments = segmentChineseWords(core);
                keywords.addAll(segments);
            }
        }
        
        // 过滤掉过短的词汇
        keywords.removeIf(keyword -> keyword.length() < 2);
        
        return keywords;
    }
    
    /**
     * 简单的中文词汇分割
     * 基于常见的学科词汇进行分割
     */
    private List<String> segmentChineseWords(String text) {
        List<String> segments = new ArrayList<>();
        
        // 定义常见的研究生学科关键词
        String[] commonWords = {
            "哲学", "经济", "法学", "教育", "文学", "历史", "理学", "工学", "农学", "医学", "管理", "艺术", "军事",
            "数学", "物理", "化学", "生物", "地理", "天文", "地质", "心理", "统计", "生态",
            "机械", "电气", "电子", "计算机", "软件", "网络", "通信", "信息", "自动化", "控制",
            "材料", "能源", "动力", "土木", "建筑", "环境", "化工", "制药", "食品", "纺织",
            "交通", "运输", "航空", "航天", "船舶", "海洋", "核", "安全", "矿业", "石油",
            "金融", "会计", "营销", "人力", "物流", "电商", "旅游", "工商", "公共",
            "临床", "基础", "预防", "中医", "药学", "护理", "口腔", "兽医", "中药",
            "马克思主义", "政治", "社会", "民族", "法律", "国际", "外交",
            "中国", "外国", "比较", "应用", "新闻", "传播", "翻译", "出版"
        };
        
        // 贪心匹配最长词汇
        int i = 0;
        while (i < text.length()) {
            boolean found = false;
            // 从最长可能的词开始匹配
            for (int len = Math.min(6, text.length() - i); len >= 2; len--) {
                String candidate = text.substring(i, i + len);
                for (String word : commonWords) {
                    if (candidate.equals(word)) {
                        segments.add(word);
                        i += len;
                        found = true;
                        break;
                    }
                }
                if (found) break;
            }
            
            if (!found) {
                // 如果没有匹配到，取单个字符（如果是有意义的）
                String singleChar = text.substring(i, i + 1);
                if (!isModifierChar(singleChar)) {
                    segments.add(singleChar);
                }
                i++;
            }
        }
        
        return segments;
    }
    
    /**
     * 判断是否为修饰性字符
     */
    private boolean isModifierChar(String ch) {
        return ch.matches("[与及和或的等学]");
    }
    
    /**
     * 判断是否为学术型硕士
     */
    public boolean isAcademicMaster() {
        return "学硕".equals(type);
    }
    
    /**
     * 判断是否为专业型硕士
     */
    public boolean isProfessionalMaster() {
        return "专硕".equals(type);
    }
    
    @Override
    public String toString() {
        return "GraduateMajor{" +
                "categoryOneCode='" + categoryOneCode + '\'' +
                ", categoryTwoCode='" + categoryTwoCode + '\'' +
                ", categoryOne='" + categoryOne + '\'' +
                ", categoryTwo='" + categoryTwo + '\'' +
                ", type='" + type + '\'' +
                ", majorName='" + majorName + '\'' +
                ", majorCode='" + majorCode + '\'' +
                ", sort=" + sort +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        GraduateMajor that = (GraduateMajor) obj;
        return majorCode != null ? majorCode.equals(that.majorCode) : that.majorCode == null;
    }
    
    @Override
    public int hashCode() {
        return majorCode != null ? majorCode.hashCode() : 0;
    }
}