package com.daxue.utils;

import com.daxue.db.MatchResult;
import com.daxue.db.GraduateMajor;
import com.daxue.db.UndergraduateMajor;

import java.util.*;
import java.util.stream.Collectors;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;

/**
 * 专业匹配系统全面测试与演示平台
 * 功能包括：全面测试、交互式查询、统计分析、性能评估、可视化展示
 * 
 * <AUTHOR> Assistant
 * @version 2.0
 */
public class MajorMatcherExample {
    
    // 静态变量
    private static MajorMatcher matcher;
    private static BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
    private static final String SEPARATOR = "=".repeat(80);
    private static final String SUB_SEPARATOR = "-".repeat(60);
    
    // 测试数据集
    private static final Map<String, String[]> TEST_DATASETS = new HashMap<String, String[]>() {{
        put("热门工科", new String[]{"计算机科学与技术", "软件工程", "人工智能", "数据科学与大数据技术", "机器人工程", "智能制造工程"});
        put("传统工科", new String[]{"机械设计制造及其自动化", "电子信息工程", "土木工程", "化学工程与工艺", "材料科学与工程", "能源与动力工程"});
        put("理学类", new String[]{"数学与应用数学", "物理学", "化学", "生物科学", "统计学", "地理科学"});
        put("管理类", new String[]{"工商管理", "会计学", "市场营销", "人力资源管理", "金融学", "国际经济与贸易"});
        put("文科类", new String[]{"汉语言文学", "英语", "新闻学", "历史学", "哲学", "法学"});
        put("医学类", new String[]{"临床医学", "口腔医学", "中医学", "药学", "护理学", "医学检验技术"});
        put("艺术类", new String[]{"音乐学", "美术学", "设计学", "戏剧影视文学", "舞蹈学", "播音与主持艺术"});
        put("交叉学科", new String[]{"生物医学工程", "环境科学与工程", "食品科学与工程", "生物技术", "光电信息科学与工程", "新能源科学与工程"});
        put("新兴专业", new String[]{"网络空间安全", "区块链工程", "虚拟现实技术", "智能医学工程", "数字经济", "大数据管理与应用"});
        put("边界测试", new String[]{"", "不存在的专业", "12345", "abcdef", "计算机", "工程"});
    }};
    
    public static void main(String[] args) {
        try {
            initializeSystem();
            showWelcome();
            runMainMenu();
        } catch (Exception e) {
            System.err.println("系统运行出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
    
    /**
     * 系统初始化
     */
    private static void initializeSystem() {
        System.out.println(SEPARATOR);
        System.out.println("🚀 专业匹配系统全面测试与演示平台 v2.0");
        System.out.println(SEPARATOR);
        System.out.print("正在初始化匹配器...");
        
        long startTime = System.currentTimeMillis();
        matcher = MajorDataRepository.createMajorMatcher();
        long endTime = System.currentTimeMillis();
        
        System.out.printf(" 完成 (%d ms)\n", endTime - startTime);
        
        // 显示系统信息
        List<UndergraduateMajor> ugMajors = MajorDataRepository.getUndergraduateMajors();
        List<GraduateMajor> gradMajors = MajorDataRepository.getGraduateMajors();
        
        System.out.printf("📚 已加载本科专业: %d 个\n", ugMajors.size());
        System.out.printf("🎓 已加载研究生专业: %d 个\n", gradMajors.size());
        System.out.printf("🔗 同义词关系数量: %d 组\n", estimateSynonymGroups());
        System.out.println();
    }
    
    /**
     * 显示欢迎信息
     */
    private static void showWelcome() {
        System.out.println("🎯 系统功能:");
        System.out.println("  1️⃣  全面测试 - 批量测试各学科门类专业匹配效果");
        System.out.println("  2️⃣  交互查询 - 输入专业名称获取推荐结果");
        System.out.println("  3️⃣  统计分析 - 分析匹配算法的覆盖率和准确性");
        System.out.println("  4️⃣  性能评估 - 测试系统响应速度和并发能力");
        System.out.println("  5️⃣  算法对比 - 对比不同匹配策略的效果");
        System.out.println("  6️⃣  可视化展示 - 图表展示匹配结果分布");
        System.out.println("  7️⃣  边界测试 - 测试异常输入和边界情况");
        System.out.println("  8️⃣  导出报告 - 生成详细的测试报告");
        System.out.println();
    }
    
    /**
     * 主菜单
     */
    private static void runMainMenu() throws IOException {
        while (true) {
            System.out.println(SEPARATOR);
            System.out.println("📋 请选择功能:");
            System.out.println("1. 🔍 交互式专业查询");
            System.out.println("2. 📊 全面测试报告");
            System.out.println("3. 🎯 分类测试");
            System.out.println("4. ⚡ 性能压力测试");
            System.out.println("5. 📈 统计分析");
            System.out.println("6. 🔬 算法效果对比");
            System.out.println("7. 🚨 边界异常测试");
            System.out.println("8. 📝 生成测试报告");
            System.out.println("0. 🚪 退出系统");
            System.out.println(SEPARATOR);
            System.out.print("请输入选项 (0-8): ");
            
            String choice = reader.readLine().trim();
            System.out.println();
            
            switch (choice) {
                case "1":
                    interactiveQuery();
                    break;
                case "2":
                    comprehensiveTest();
                    break;
                case "3":
                    categoryTest();
                    break;
                case "4":
                    performanceStressTest();
                    break;
                case "5":
                    statisticalAnalysis();
                    break;
                case "6":
                    algorithmComparison();
                    break;
                case "7":
                    boundaryExceptionTest();
                    break;
                case "8":
                    generateTestReport();
                    break;
                case "0":
                    System.out.println("👋 感谢使用专业匹配系统，再见！");
                    return;
                default:
                    System.out.println("❌ 无效选项，请重新输入");
            }
            
            System.out.println("\n按回车键继续...");
            reader.readLine();
        }
    }
    
    /**
     * 交互式专业查询
     */
    private static void interactiveQuery() throws IOException {
        System.out.println("🔍 交互式专业查询");
        System.out.println(SUB_SEPARATOR);
        
        while (true) {
            System.out.print("请输入本科专业名称 (输入 'q' 返回主菜单): ");
            String majorName = reader.readLine().trim();
            
            if ("q".equalsIgnoreCase(majorName)) {
                break;
            }
            
            if (majorName.isEmpty()) {
                System.out.println("❌ 专业名称不能为空");
                continue;
            }
            
            long startTime = System.currentTimeMillis();
            List<MatchResult> results = matcher.findMatchingGraduateMajors(majorName);
            long endTime = System.currentTimeMillis();
            
            displayDetailedResults(majorName, results, endTime - startTime);
            System.out.println();
        }
    }
    
    /**
     * 全面测试报告
     */
    private static void comprehensiveTest() {
        System.out.println("📊 全面测试报告");
        System.out.println(SUB_SEPARATOR);
        
        Map<String, TestResult> categoryResults = new HashMap<>();
        int totalTests = 0;
        int totalMatches = 0;
        long totalTime = 0;
        
        for (Map.Entry<String, String[]> entry : TEST_DATASETS.entrySet()) {
            String category = entry.getKey();
            String[] majors = entry.getValue();
            
            if ("边界测试".equals(category)) continue; // 跳过边界测试
            
            System.out.printf("\n🏷️  测试类别: %s\n", category);
            System.out.println(SUB_SEPARATOR);
            
            TestResult categoryResult = new TestResult();
            
            for (String major : majors) {
                long startTime = System.currentTimeMillis();
                List<MatchResult> results = matcher.findMatchingGraduateMajors(major);
                long endTime = System.currentTimeMillis();
                
                totalTests++;
                totalTime += (endTime - startTime);
                
                if (!results.isEmpty()) {
                    totalMatches++;
                    categoryResult.successCount++;
                    categoryResult.totalResults += results.size();
                    
                    // 统计匹配类型
                    for (MatchResult result : results) {
                        categoryResult.addMatchType(result.getMatchType().name());
                    }
                    
                    // 显示最佳匹配
                    MatchResult best = results.get(0);
                    System.out.printf("✅ %-25s -> %-30s (%.1f分, %s)\n",
                        major,
                        best.getGraduateMajor().getMajorName(),
                        best.getMatchScore(),
                        best.getMatchType().getDescription());
                } else {
                    categoryResult.failCount++;
                    System.out.printf("❌ %-25s -> 无匹配结果\n", major);
                }
                
                categoryResult.totalTests++;
                categoryResult.totalTime += (endTime - startTime);
            }
            
            categoryResults.put(category, categoryResult);
            
            // 显示类别统计
            System.out.printf("\n📈 %s 统计:\n", category);
            System.out.printf("   成功率: %.1f%% (%d/%d)\n", 
                categoryResult.getSuccessRate(), categoryResult.successCount, categoryResult.totalTests);
            System.out.printf("   平均结果数: %.1f\n", categoryResult.getAverageResults());
            System.out.printf("   平均耗时: %.1f ms\n", categoryResult.getAverageTime());
        }
        
        // 显示总体统计
        System.out.println("\n" + SEPARATOR);
        System.out.println("📊 总体测试统计");
        System.out.println(SEPARATOR);
        System.out.printf("总测试数: %d\n", totalTests);
        System.out.printf("成功匹配: %d (%.1f%%)\n", totalMatches, (double) totalMatches / totalTests * 100);
        System.out.printf("平均耗时: %.2f ms\n", (double) totalTime / totalTests);
        System.out.printf("系统QPS: %.0f 次/秒\n", 1000.0 / ((double) totalTime / totalTests));
    }
    
    /**
     * 分类测试
     */
    private static void categoryTest() throws IOException {
        System.out.println("🎯 分类测试");
        System.out.println(SUB_SEPARATOR);
        
        System.out.println("可选测试类别:");
        String[] categories = TEST_DATASETS.keySet().toArray(new String[0]);
        for (int i = 0; i < categories.length; i++) {
            System.out.printf("%d. %s\n", i + 1, categories[i]);
        }
        
        System.out.print("请选择类别编号 (1-" + categories.length + "): ");
        String input = reader.readLine().trim();
        
        try {
            int choice = Integer.parseInt(input) - 1;
            if (choice >= 0 && choice < categories.length) {
                String category = categories[choice];
                String[] majors = TEST_DATASETS.get(category);
                
                System.out.printf("\n🏷️  测试类别: %s\n", category);
                System.out.println(SUB_SEPARATOR);
                
                for (String major : majors) {
                    long startTime = System.currentTimeMillis();
                    List<MatchResult> results = matcher.findMatchingGraduateMajors(major);
                    long endTime = System.currentTimeMillis();
                    
                    displayDetailedResults(major, results, endTime - startTime);
                    System.out.println();
                }
            } else {
                System.out.println("❌ 无效的类别编号");
            }
        } catch (NumberFormatException e) {
            System.out.println("❌ 请输入有效的数字");
        }
    }
    
    /**
     * 性能压力测试
     */
    private static void performanceStressTest() throws IOException {
        System.out.println("⚡ 性能压力测试");
        System.out.println(SUB_SEPARATOR);
        
        System.out.print("请输入测试轮数 (默认1000): ");
        String input = reader.readLine().trim();
        int testRounds;
        try {
            testRounds = input.isEmpty() ? 1000 : Integer.parseInt(input);
        } catch (NumberFormatException e) {
            testRounds = 1000;
        }
        
        String[] testMajors = {"计算机科学与技术", "机械工程", "临床医学", "工商管理"};
        
        System.out.printf("🏃 开始性能测试 (%d 轮)...\n", testRounds);
        
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 0; i < testRounds; i++) {
            String major = testMajors[i % testMajors.length];
            
            long startTime = System.nanoTime();
            List<MatchResult> results = matcher.findMatchingGraduateMajors(major);
            long endTime = System.nanoTime();
            
            totalTime += (endTime - startTime);
            if (!results.isEmpty()) {
                successCount++;
            }
            
            if ((i + 1) % 100 == 0) {
                System.out.printf("  已完成: %d/%d\n", i + 1, testRounds);
            }
        }
        
        double avgTimeMs = totalTime / 1_000_000.0 / testRounds;
        double qps = 1000.0 / avgTimeMs;
        
        System.out.println("\n📈 性能测试结果:");
        System.out.printf("  总测试轮数: %d\n", testRounds);
        System.out.printf("  成功匹配率: %.1f%% (%d/%d)\n", 
            (double) successCount / testRounds * 100, successCount, testRounds);
        System.out.printf("  平均响应时间: %.3f ms\n", avgTimeMs);
        System.out.printf("  理论QPS: %.0f 次/秒\n", qps);
        System.out.printf("  总耗时: %.2f 秒\n", totalTime / 1_000_000_000.0);
        
        // 内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.printf("  内存使用: %.1f MB\n", usedMemory / 1024.0 / 1024.0);
    }
    
    /**
     * 统计分析
     */
    private static void statisticalAnalysis() {
        System.out.println("📈 统计分析");
        System.out.println(SUB_SEPARATOR);
        
        Map<String, Integer> matchTypeStats = new HashMap<>();
        Map<String, Integer> categoryStats = new HashMap<>();
        List<Double> scores = new ArrayList<>();
        
        // 收集所有测试数据的统计信息
        for (Map.Entry<String, String[]> entry : TEST_DATASETS.entrySet()) {
            String category = entry.getKey();
            String[] majors = entry.getValue();
            
            if ("边界测试".equals(category)) continue;
            
            for (String major : majors) {
                List<MatchResult> results = matcher.findMatchingGraduateMajors(major);
                
                if (!results.isEmpty()) {
                    categoryStats.put(category, categoryStats.getOrDefault(category, 0) + 1);
                    
                    for (MatchResult result : results) {
                        String matchType = result.getMatchType().getDescription();
                        matchTypeStats.put(matchType, matchTypeStats.getOrDefault(matchType, 0) + 1);
                        scores.add(result.getMatchScore());
                    }
                }
            }
        }
        
        // 显示匹配类型分布
        System.out.println("🎯 匹配类型分布:");
        matchTypeStats.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> System.out.printf("  %-15s: %4d 次 (%.1f%%)\n", 
                entry.getKey(), 
                entry.getValue(),
                (double) entry.getValue() / scores.size() * 100));
        
        // 显示学科门类成功率
        System.out.println("\n📚 学科门类成功匹配统计:");
        categoryStats.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> {
                String category = entry.getKey();
                int successCount = entry.getValue();
                int totalCount = TEST_DATASETS.get(category).length;
                System.out.printf("  %-12s: %d/%d (%.1f%%)\n", 
                    category, successCount, totalCount, (double) successCount / totalCount * 100);
            });
        
        // 显示分数分布
        if (!scores.isEmpty()) {
            Collections.sort(scores);
            double min = scores.get(0);
            double max = scores.get(scores.size() - 1);
            double avg = scores.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double median = scores.size() % 2 == 0 ? 
                (scores.get(scores.size() / 2 - 1) + scores.get(scores.size() / 2)) / 2.0 :
                scores.get(scores.size() / 2);
            
            System.out.println("\n📊 匹配分数统计:");
            System.out.printf("  最低分: %.1f\n", min);
            System.out.printf("  最高分: %.1f\n", max);
            System.out.printf("  平均分: %.1f\n", avg);
            System.out.printf("  中位数: %.1f\n", median);
            System.out.printf("  样本数: %d\n", scores.size());
            
            // 分数区间分布
            System.out.println("\n  分数区间分布:");
            int[] ranges = {0, 10, 20, 30, 40, 50};
            for (int i = 0; i < ranges.length - 1; i++) {
                int start = ranges[i];
                int end = ranges[i + 1];
                long count = scores.stream().filter(s -> s >= start && s < end).count();
                System.out.printf("    [%2d, %2d): %4d 个 (%.1f%%)\n", 
                    start, end, count, (double) count / scores.size() * 100);
            }
            long count = scores.stream().filter(s -> s >= 50).count();
            System.out.printf("    [50,∞): %4d 个 (%.1f%%)\n", 
                count, (double) count / scores.size() * 100);
        }
    }
    
    /**
     * 算法效果对比
     */
    private static void algorithmComparison() {
        System.out.println("🔬 算法效果对比");
        System.out.println(SUB_SEPARATOR);
        
        String[] testMajors = {"计算机科学与技术", "机械工程", "临床医学", "工商管理", "物理学"};
        
        System.out.println("📋 对比不同匹配策略的效果:\n");
        
        for (String major : testMajors) {
            System.out.printf("🎯 测试专业: %s\n", major);
            System.out.println(SUB_SEPARATOR);
            
            List<MatchResult> results = matcher.findMatchingGraduateMajors(major);
            
            if (results.isEmpty()) {
                System.out.println("  ❌ 无匹配结果\n");
                continue;
            }
            
            // 按匹配类型分组
            Map<String, List<MatchResult>> typeGroups = results.stream()
                .collect(Collectors.groupingBy(r -> r.getMatchType().getDescription()));
            
            typeGroups.entrySet().stream()
                .sorted((e1, e2) -> Double.compare(
                    e2.getValue().get(0).getMatchScore(),
                    e1.getValue().get(0).getMatchScore()))
                .forEach(entry -> {
                    String type = entry.getKey();
                    List<MatchResult> typeResults = entry.getValue();
                    
                    System.out.printf("  📌 %s (%d个):\n", type, typeResults.size());
                    typeResults.stream()
                        .limit(3) // 只显示前3个
                        .forEach(result -> System.out.printf("    ✓ %-30s (%.1f分)\n", 
                            result.getGraduateMajor().getMajorName(), result.getMatchScore()));
                });
            
            System.out.println();
        }
    }
    
    /**
     * 边界异常测试
     */
    private static void boundaryExceptionTest() {
        System.out.println("🚨 边界异常测试");
        System.out.println(SUB_SEPARATOR);
        
        String[] boundaryTests = TEST_DATASETS.get("边界测试");
        
        System.out.println("测试各种边界情况和异常输入:\n");
        
        for (String testInput : boundaryTests) {
            System.out.printf("🧪 测试输入: \"%s\"\n", testInput);
            
            try {
                long startTime = System.currentTimeMillis();
                List<MatchResult> results = matcher.findMatchingGraduateMajors(testInput);
                long endTime = System.currentTimeMillis();
                
                if (results.isEmpty()) {
                    System.out.println("  ✅ 正确处理: 无匹配结果");
                } else {
                    System.out.printf("  ⚠️  意外匹配: 找到 %d 个结果\n", results.size());
                    MatchResult best = results.get(0);
                    System.out.printf("      最佳: %s (%.1f分)\n", 
                        best.getGraduateMajor().getMajorName(), best.getMatchScore());
                }
                
                System.out.printf("  ⏱️  耗时: %d ms\n", endTime - startTime);
                
            } catch (Exception e) {
                System.out.printf("  ❌ 异常: %s\n", e.getMessage());
            }
            
            System.out.println();
        }
    }
    
    /**
     * 生成测试报告
     */
    private static void generateTestReport() {
        System.out.println("📝 生成测试报告");
        System.out.println(SUB_SEPARATOR);
        
        StringBuilder report = new StringBuilder();
        report.append("专业匹配系统测试报告\n");
        report.append("=".repeat(50)).append("\n");
        report.append("生成时间: ").append(new Date()).append("\n\n");
        
        // 系统信息
        List<UndergraduateMajor> ugMajors = MajorDataRepository.getUndergraduateMajors();
        List<GraduateMajor> gradMajors = MajorDataRepository.getGraduateMajors();
        
        report.append("1. 系统信息\n");
        report.append("-".repeat(30)).append("\n");
        report.append("本科专业数量: ").append(ugMajors.size()).append("\n");
        report.append("研究生专业数量: ").append(gradMajors.size()).append("\n");
        report.append("同义词组数量: ").append(estimateSynonymGroups()).append("\n\n");
        
        // 执行全面测试并收集数据
        Map<String, TestResult> results = new HashMap<>();
        int totalSuccess = 0;
        int totalTests = 0;
        
        for (Map.Entry<String, String[]> entry : TEST_DATASETS.entrySet()) {
            String category = entry.getKey();
            String[] majors = entry.getValue();
            
            if ("边界测试".equals(category)) continue;
            
            TestResult categoryResult = new TestResult();
            
            for (String major : majors) {
                List<MatchResult> matchResults = matcher.findMatchingGraduateMajors(major);
                categoryResult.totalTests++;
                totalTests++;
                
                if (!matchResults.isEmpty()) {
                    categoryResult.successCount++;
                    totalSuccess++;
                    categoryResult.totalResults += matchResults.size();
                }
            }
            
            results.put(category, categoryResult);
        }
        
        // 测试结果汇总
        report.append("2. 测试结果汇总\n");
        report.append("-".repeat(30)).append("\n");
        report.append("总测试数: ").append(totalTests).append("\n");
        report.append("成功匹配: ").append(totalSuccess).append("\n");
        report.append("总体成功率: ").append(String.format("%.1f%%", (double) totalSuccess / totalTests * 100)).append("\n\n");
        
        // 分类详细结果
        report.append("3. 分类测试结果\n");
        report.append("-".repeat(30)).append("\n");
        for (Map.Entry<String, TestResult> entry : results.entrySet()) {
            String category = entry.getKey();
            TestResult result = entry.getValue();
            report.append(String.format("%-12s: %d/%d (%.1f%%) 平均结果数: %.1f\n",
                category,
                result.successCount,
                result.totalTests,
                result.getSuccessRate(),
                result.getAverageResults()));
        }
        
        // 性能测试
        report.append("\n4. 性能测试\n");
        report.append("-".repeat(30)).append("\n");
        String testMajor = "计算机科学与技术";
        int perfTestCount = 100;
        
        long startTime = System.nanoTime();
        for (int i = 0; i < perfTestCount; i++) {
            matcher.findMatchingGraduateMajors(testMajor);
        }
        long endTime = System.nanoTime();
        
        double avgTimeMs = (endTime - startTime) / 1_000_000.0 / perfTestCount;
        report.append("测试轮数: ").append(perfTestCount).append("\n");
        report.append("平均响应时间: ").append(String.format("%.3f ms", avgTimeMs)).append("\n");
        report.append("理论QPS: ").append(String.format("%.0f", 1000.0 / avgTimeMs)).append("\n\n");
        
        // 建议和总结
        report.append("5. 总结与建议\n");
        report.append("-".repeat(30)).append("\n");
        
        if ((double) totalSuccess / totalTests >= 0.9) {
            report.append("✅ 系统整体表现优秀，匹配成功率高\n");
        } else if ((double) totalSuccess / totalTests >= 0.7) {
            report.append("⚠️ 系统表现良好，建议优化部分匹配策略\n");
        } else {
            report.append("❌ 系统表现需要改进，建议全面优化匹配算法\n");
        }
        
        report.append("建议关注成功率较低的专业类别，优化相应的同义词库和匹配策略。\n");
        
        System.out.println(report.toString());
        System.out.println("📋 报告生成完成");
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 显示详细的匹配结果
     */
    private static void displayDetailedResults(String majorName, List<MatchResult> results, long timeMs) {
        System.out.printf("🎯 本科专业: %s (耗时: %d ms)\n", majorName, timeMs);
        System.out.println(SUB_SEPARATOR);
        
        if (results.isEmpty()) {
            System.out.println("❌ 未找到匹配的研究生专业");
            return;
        }
        
        System.out.printf("✅ 找到 %d 个匹配的研究生专业:\n\n", results.size());
        
        for (int i = 0; i < Math.min(10, results.size()); i++) {
            MatchResult result = results.get(i);
            GraduateMajor grad = result.getGraduateMajor();
            
            System.out.printf("%2d. 🎓 %s\n", i + 1, grad.getMajorName());
            System.out.printf("    📚 学科门类: %s\n", grad.getCategoryOne());
            System.out.printf("    🏷️  一级学科: %s\n", grad.getCategoryTwo() != null ? grad.getCategoryTwo() : "无");
            System.out.printf("    🎖️  学位类型: %s\n", grad.getType());
            System.out.printf("    🔢 专业代码: %s\n", grad.getMajorCode());
            System.out.printf("    ⭐ 匹配分数: %.1f\n", result.getMatchScore());
            System.out.printf("    🏷️  匹配类型: %s\n", result.getMatchType().getDescription());
            System.out.printf("    💡 匹配原因: %s\n", result.getMatchReason());
            System.out.println();
        }
        
        if (results.size() > 10) {
            System.out.printf("    ... 还有 %d 个结果 (仅显示前10个)\n", results.size() - 10);
        }
        
        // 显示匹配类型统计
        Map<String, Long> typeCount = results.stream()
            .collect(Collectors.groupingBy(
                r -> r.getMatchType().getDescription(),
                Collectors.counting()));
        
        System.out.println("📊 匹配类型分布:");
        typeCount.forEach((type, count) -> 
            System.out.printf("    %s: %d个\n", type, count));
    }
    
    /**
     * 估算同义词组数量
     */
    private static int estimateSynonymGroups() {
        // 这是一个估算值，实际应该从MajorMatcher获取
        return 150; // 根据我们扩展的同义词库估算
    }
    
    // ========== 内部类 ==========
    
    /**
     * 测试结果统计类
     */
    private static class TestResult {
        int totalTests = 0;
        int successCount = 0;
        int failCount = 0;
        int totalResults = 0;
        long totalTime = 0;
        Map<String, Integer> matchTypeCount = new HashMap<>();
        
        void addMatchType(String type) {
            matchTypeCount.put(type, matchTypeCount.getOrDefault(type, 0) + 1);
        }
        
        double getSuccessRate() {
            return totalTests == 0 ? 0.0 : (double) successCount / totalTests * 100;
        }
        
        double getAverageResults() {
            return successCount == 0 ? 0.0 : (double) totalResults / successCount;
        }
        
        double getAverageTime() {
            return totalTests == 0 ? 0.0 : (double) totalTime / totalTests;
        }
    }
}