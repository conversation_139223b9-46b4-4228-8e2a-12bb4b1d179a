package com.daxue.utils;

import com.daxue.db.UndergraduateMajor;
import com.daxue.db.GraduateMajor;
import com.daxue.db.MatchResult;
import com.daxue.db.MatchResult.MatchType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 专业匹配算法工具类
 * 实现本科专业到研究生专业的智能匹配
 */
public class MajorMatcher {
    
    /** 本科专业数据 */
    private List<UndergraduateMajor> undergraduateMajors;
    
    /** 研究生专业数据 */
    private List<GraduateMajor> graduateMajors;
    
    /** 同义词词典 */
    private Map<String, Set<String>> synonymDict;
    
    /** 跨学科映射关系 */
    private Map<String, Set<String>> crossDisciplineMap;
    
    /** 匹配权重配置 */
    private static final double EXACT_MATCH_WEIGHT = 0.4;
    private static final double KEYWORD_MATCH_WEIGHT = 0.3;
    private static final double CATEGORY_MATCH_WEIGHT = 0.2;
    private static final double CROSS_DISCIPLINE_WEIGHT = 0.1;
    
    public MajorMatcher(List<UndergraduateMajor> undergraduateMajors, List<GraduateMajor> graduateMajors) {
        this.undergraduateMajors = undergraduateMajors != null ? undergraduateMajors : new ArrayList<>();
        this.graduateMajors = graduateMajors != null ? graduateMajors : new ArrayList<>();
        this.synonymDict = new HashMap<>();
        this.crossDisciplineMap = new HashMap<>();
        initializeSynonymDict();
        initializeCrossDisciplineMap();
    }
    
    /**
     * 加载本科专业数据
     */
    public void loadUndergraduateMajors(List<UndergraduateMajor> majors) {
        this.undergraduateMajors = majors != null ? majors : new ArrayList<>();
    }
    
    /**
     * 加载研究生专业数据
     */
    public void loadGraduateMajors(List<GraduateMajor> majors) {
        this.graduateMajors = majors != null ? majors : new ArrayList<>();
    }
    
    /**
     * 主匹配方法 - 根据本科专业名称找到匹配的研究生专业
     * @param undergraduateMajorName 本科专业名称
     * @return 匹配结果列表，按匹配分数降序排列
     */
    public List<MatchResult> findMatchingGraduateMajors(String undergraduateMajorName) {
        if (undergraduateMajorName == null || undergraduateMajorName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<MatchResult> allResults = new ArrayList<>();
        
        // 1. 精确匹配
        allResults.addAll(exactMatch(undergraduateMajorName));
        
        // 2. 关键词匹配
        allResults.addAll(keywordMatch(undergraduateMajorName));
        
        // 3. 同义词匹配
        allResults.addAll(synonymMatch(undergraduateMajorName));
        
        // 4. 学科门类匹配
        allResults.addAll(categoryMatch(undergraduateMajorName));
        
        // 5. 交叉学科匹配
        allResults.addAll(crossDisciplineMatch(undergraduateMajorName));
        
        // 去重并按分数排序
        return deduplicateAndSort(allResults);
    }
    
    /**
     * 精确匹配 - 专业名称完全相同
     */
    private List<MatchResult> exactMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();
        
        for (GraduateMajor grad : graduateMajors) {
            if (majorName.equals(grad.getMajorName())) {
                double score = 100.0 * EXACT_MATCH_WEIGHT;
                results.add(new MatchResult(grad, score, MatchType.EXACT_MATCH, 
                    "专业名称完全匹配"));
            }
        }
        
        return results;
    }
    
    /**
     * 关键词匹配 - 核心专业词汇匹配
     */
    private List<MatchResult> keywordMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();
        
        // 创建临时的本科专业对象来提取关键词
        UndergraduateMajor tempMajor = new UndergraduateMajor();
        tempMajor.setMajorName(majorName);
        String ugKeywordsStr = tempMajor.getCoreKeywords();
        Set<String> undergraduateKeywords = new HashSet<>();
        if (!ugKeywordsStr.isEmpty()) {
            // 修复：使用逗号分割关键词，而不是按字符分割
            undergraduateKeywords.addAll(Arrays.asList(ugKeywordsStr.split(",")));
            undergraduateKeywords.removeIf(String::isEmpty); // 移除空字符串
        }
        
        if (undergraduateKeywords.isEmpty()) {
            return results;
        }
        
        for (GraduateMajor grad : graduateMajors) {
            // 研究生专业也需要提取关键词
            String gradKeywordsStr = grad.getCoreKeywords();
            Set<String> graduateKeywords = new HashSet<>();
            if (!gradKeywordsStr.isEmpty()) {
                // 修复：使用逗号分割关键词，而不是按字符分割
                graduateKeywords.addAll(Arrays.asList(gradKeywordsStr.split(",")));
                graduateKeywords.removeIf(String::isEmpty); // 移除空字符串
            }

            if (!graduateKeywords.isEmpty()) {
                double similarity = calculateSetSimilarity(undergraduateKeywords, graduateKeywords);
                if (similarity > 0.3) { // 相似度阈值
                    double score = similarity * 100.0 * KEYWORD_MATCH_WEIGHT;
                    results.add(new MatchResult(grad, score, MatchType.KEYWORD_MATCH, 
                        String.format("关键词相似度: %.2f", similarity)));
                }
            }
        }
        
        return results;
    }
    
    /**
     * 同义词匹配
     */
    private List<MatchResult> synonymMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();
        
        for (GraduateMajor grad : graduateMajors) {
            if (areSynonyms(majorName, grad.getMajorName())) {
                double score = 90.0 * KEYWORD_MATCH_WEIGHT;
                results.add(new MatchResult(grad, score, MatchType.SYNONYM_MATCH, 
                    "基于同义词词典匹配"));
            }
        }
        
        return results;
    }
    
    /**
     * 学科门类匹配 - 同一学科门类下的相关专业
     */
    private List<MatchResult> categoryMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();
        
        // 找到本科专业的学科门类
        String categoryCode = findCategoryCodeByMajorName(majorName);
        if (categoryCode == null) {
            return results;
        }
        
        for (GraduateMajor grad : graduateMajors) {
            if (categoryCode.equals(grad.getCategoryOneCode())) {
                double score = 60.0 * CATEGORY_MATCH_WEIGHT;
                results.add(new MatchResult(grad, score, MatchType.CATEGORY_MATCH, 
                    "同属" + grad.getCategoryOne() + "学科门类"));
            }
        }
        
        return results;
    }
    
    /**
     * 交叉学科匹配 - 跨学科但相关的专业
     */
    private List<MatchResult> crossDisciplineMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();
        
        String categoryCode = findCategoryCodeByMajorName(majorName);
        if (categoryCode == null) {
            return results;
        }
        
        Set<String> relatedCategories = crossDisciplineMap.get(categoryCode);
        if (relatedCategories != null) {
            for (GraduateMajor grad : graduateMajors) {
                if (relatedCategories.contains(grad.getCategoryOneCode())) {
                    double score = 40.0 * CROSS_DISCIPLINE_WEIGHT;
                    results.add(new MatchResult(grad, score, MatchType.CROSS_DISCIPLINE_MATCH, 
                        "跨学科相关专业"));
                }
            }
        }
        
        return results;
    }
    
    /**
     * 计算关键词集合相似度 (Jaccard 相似度)
     */
    private double calculateSetSimilarity(Set<String> set1, Set<String> set2) {
        if (set1 == null || set2 == null || set1.isEmpty() || set2.isEmpty()) {
            return 0.0;
        }
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return (double) intersection.size() / union.size();
    }
    
    /**
     * 计算编辑距离
     */
    private int calculateEditDistance(String str1, String str2) {
        int m = str1.length();
        int n = str2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 判断两个专业名称是否为同义词
     */
    private boolean areSynonyms(String major1, String major2) {
        for (Set<String> synonyms : synonymDict.values()) {
            if (synonyms.contains(major1) && synonyms.contains(major2)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 根据专业名称查找学科门类代码
     */
    private String findCategoryCodeByMajorName(String majorName) {
        for (UndergraduateMajor major : undergraduateMajors) {
            if (majorName.equals(major.getMajorName())) {
                return major.getCategoryOneCode();
            }
        }
        return null;
    }
    
    /**
     * 去重并按分数排序
     */
    private List<MatchResult> deduplicateAndSort(List<MatchResult> results) {
        // 按专业代码去重，保留分数最高的
        Map<String, MatchResult> uniqueResults = new HashMap<>();
        
        for (MatchResult result : results) {
            String majorCode = result.getGraduateMajor().getMajorCode();
            if (!uniqueResults.containsKey(majorCode) || 
                uniqueResults.get(majorCode).getMatchScore() < result.getMatchScore()) {
                uniqueResults.put(majorCode, result);
            }
        }
        
        // 按分数降序排序
        return uniqueResults.values().stream()
                .sorted((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()))
                .limit(10) // 最多返回10个结果
                .collect(Collectors.toList());
    }
    
    /**
     * 初始化同义词词典
     */
    private void initializeSynonymDict() {
        initializeBasicSynonyms();
        initializeEngineeringSynonyms();
        initializeScienceSynonyms();
        initializeMedicalSynonyms();
        initializeManagementSynonyms();
        initializeArtsSynonyms();
        initializeEmergingSynonyms();
    }
    
    /**
     * 初始化基础学科同义词
     */
    private void initializeBasicSynonyms() {
        // === 哲学门类 ===
        addSynonyms("哲学", "哲学", "马克思主义哲学", "中国哲学", "外国哲学", "逻辑学", "伦理学", "美学", "宗教学", "科学技术哲学");
        addSynonyms("马克思主义理论", "马克思主义基本原理", "马克思主义发展史", "马克思主义中国化研究", "思想政治教育", "中国近现代史基本问题研究");
        
        // === 经济学门类 ===
        addSynonyms("经济学", "经济学", "理论经济学", "应用经济学", "政治经济学", "西方经济学", "世界经济", "国民经济学", "区域经济学");
        addSynonyms("金融学", "金融学", "金融工程", "保险学", "投资学", "金融数学", "信用管理", "经济与金融", "精算学", "互联网金融", "金融科技");
        addSynonyms("财政学", "财政学", "税收学", "税务");
        addSynonyms("国际贸易", "国际经济与贸易", "贸易经济", "国际贸易学", "国际商务");
        addSynonyms("统计学", "统计学", "经济统计学", "应用统计学", "应用统计", "数量经济学");
        
        // === 法学门类 ===
        addSynonyms("法学", "法学", "法学理论", "法律史", "宪法学与行政法学", "刑法学", "民商法学", "诉讼法学", "经济法学", "环境与资源保护法学", "国际法学", "军事法学");
        addSynonyms("政治学", "政治学与行政学", "国际政治", "外交学", "政治学理论", "中外政治制度", "国际关系", "国际事务与国际关系");
        addSynonyms("社会学", "社会学", "社会工作", "人类学", "民俗学", "人口学", "女性学", "家政学", "老年学", "社会政策");
        
        // === 教育学门类 ===
        addSynonyms("教育学", "教育学", "教育学原理", "课程与教学论", "教育史", "比较教育学", "学前教育学", "高等教育学", "成人教育学", "职业技术教育学", "特殊教育学", "教育技术学");
        addSynonyms("体育学", "体育教育", "运动训练", "社会体育指导与管理", "武术与民族传统体育", "运动人体科学", "运动康复", "休闲体育");
        addSynonyms("心理学", "心理学", "应用心理学", "基础心理学", "发展与教育心理学", "心理健康教育");
        
        // === 文学门类 ===
        addSynonyms("中国语言文学", "汉语言文学", "汉语言", "汉语国际教育", "中国少数民族语言文学", "古典文献学", "应用语言学", "秘书学");
        addSynonyms("外国语言文学", "英语", "俄语", "德语", "法语", "西班牙语", "阿拉伯语", "日语", "朝鲜语", "翻译", "商务英语");
        addSynonyms("新闻传播学", "新闻学", "广播电视学", "广告学", "传播学", "编辑出版学", "网络与新媒体", "数字出版", "新闻与传播", "出版");
        
        // === 历史学门类 ===
        addSynonyms("历史学", "历史学", "世界史", "考古学", "文物与博物馆学", "文物保护技术", "中国史", "博物馆");
    }
    
    /**
     * 初始化理学门类同义词
     */
    private void initializeScienceSynonyms() {
        addSynonyms("数学", "数学与应用数学", "信息与计算科学", "数理基础科学", "数据计算及应用", "基础数学", "计算数学", "概率论与数理统计", "应用数学", "运筹学与控制论");
        addSynonyms("物理学", "物理学", "应用物理学", "核物理", "声学", "量子信息科学", "理论物理", "粒子物理与原子核物理", "凝聚态物理", "光学");
        addSynonyms("化学", "化学", "应用化学", "化学生物学", "分子科学与工程", "能源化学", "无机化学", "分析化学", "有机化学", "物理化学", "高分子化学与物理");
        addSynonyms("生物科学", "生物科学", "生物技术", "生物信息学", "生态学", "整合科学", "神经科学", "植物学", "动物学", "生理学", "微生物学", "遗传学", "细胞生物学", "生物化学与分子生物学");
        addSynonyms("地理科学", "地理科学", "自然地理与资源环境", "人文地理与城乡规划", "地理信息科学", "自然地理学", "人文地理学", "地图学与地理信息系统");
        addSynonyms("大气科学", "大气科学", "应用气象学", "气象学", "大气物理学与大气环境", "气象");
        addSynonyms("海洋科学", "海洋科学", "海洋技术", "海洋资源与环境", "物理海洋学", "海洋化学", "海洋生物学", "海洋地质");
        addSynonyms("地质学", "地质学", "地球化学", "地球信息科学与技术", "古生物学", "矿物学、岩石学、矿床学", "构造地质学");
        addSynonyms("天文学", "天文学", "天体物理", "天体测量与天体力学");
        addSynonyms("地球物理学", "地球物理学", "空间科学与技术", "地球与空间科学", "固体地球物理学", "空间物理学");
    }
    
    /**
     * 初始化工学门类同义词
     */
    private void initializeEngineeringSynonyms() {
        addSynonyms("力学", "理论与应用力学", "工程力学", "一般力学与力学基础", "固体力学", "流体力学");
        
        addSynonyms("机械工程", "机械工程", "机械设计制造及其自动化", "材料成型及控制工程", "机械电子工程", "工业设计", "过程装备与控制工程", "车辆工程", "汽车服务工程", "机械工艺技术", "微机电系统工程", "机电技术教育", "汽车维修工程教育", "智能制造工程", "智能车辆工程", "仿生科学与工程", "新能源汽车工程", "机械制造及其自动化", "机械设计及理论", "机械工程", "车辆工程", "航空工程", "航天工程", "船舶工程", "兵器工程", "工业设计工程", "农机装备工程", "智能制造技术", "机器人工程");
        
        addSynonyms("仪器科学与技术", "测控技术与仪器", "精密仪器", "精密仪器及机械", "测试计量技术及仪器", "仪器仪表工程");
        
        addSynonyms("材料科学与工程", "材料科学与工程", "材料物理", "材料化学", "冶金工程", "金属材料工程", "无机非金属材料工程", "高分子材料与工程", "复合材料与工程", "粉体材料科学与工程", "宝石及材料工艺学", "焊接技术与工程", "功能材料", "纳米材料与技术", "新能源材料与器件", "材料设计科学与工程", "复合材料成型工程", "智能材料与结构", "材料物理与化学", "材料学", "材料加工工程", "材料工程", "冶金工程", "纺织工程", "林业工程", "轻化工程");
        
        addSynonyms("能源动力", "能源与动力工程", "能源与环境系统工程", "新能源科学与工程", "储能科学与工程", "动力工程及工程热物理", "工程热物理", "热能工程", "动力机械及工程", "流体机械及工程", "制冷及低温工程", "化工过程机械", "电气工程", "动力工程", "核能工程", "航空发动机工程", "燃气轮机工程", "航天动力工程", "清洁能源技术", "储能技术");
        
        addSynonyms("电气工程", "电气工程及其自动化", "智能电网信息工程", "光源与照明", "电气工程与智能控制", "电机电器智能化", "电缆工程", "电机与电器", "电力系统及其自动化", "高电压与绝缘技术", "电力电子与电力传动", "电工理论与新技术");
        
        addSynonyms("电子信息", "电子信息工程", "电子科学与技术", "通信工程", "微电子科学与工程", "光电信息科学与工程", "信息工程", "广播电视工程", "水声工程", "电子封装技术", "集成电路设计与集成系统", "医学信息工程", "电磁场与无线技术", "电波传播与天线", "电子信息科学与技术", "电信工程及管理", "应用电子技术教育", "人工智能", "海洋信息工程", "柔性电子学", "物理电子学", "电路与系统", "微电子学与固体电子学", "电磁场与微波技术", "信息与通信工程", "通信与信息系统", "信号与信息处理", "新一代电子信息技术", "集成电路工程", "光电信息工程", "网络与信息安全");
        
        addSynonyms("自动化", "自动化", "轨道交通信号与控制", "机器人工程", "邮政工程", "核电技术与控制工程", "智能装备与系统", "控制科学与工程", "控制理论与控制工程", "检测技术与自动化装置", "系统工程", "模式识别与智能系统", "导航、制导与控制", "控制工程");
        
        addSynonyms("计算机科学与技术", "计算机科学与技术", "软件工程", "网络工程", "信息安全", "物联网工程", "数字媒体技术", "智能科学与技术", "空间信息与数字技术", "电子与计算机工程", "数据科学与大数据技术", "网络空间安全", "新媒体技术", "电影制作", "保密技术", "服务科学与工程", "虚拟现实技术", "区块链工程", "计算机系统结构", "计算机软件与理论", "计算机应用技术", "计算机技术", "大数据技术与工程");
        
        addSynonyms("土木工程", "土木工程", "建筑环境与能源应用工程", "给排水科学与工程", "建筑电气与智能化", "道路桥梁与渡河工程", "城市地下空间工程", "智能建造", "土木、水利与海洋工程", "岩土工程", "结构工程", "市政工程", "供热、供燃气、通风及空调工程", "防灾减灾工程及防护工程", "桥梁与隧道工程", "人工环境工程");
        
        addSynonyms("水利工程", "水利水电工程", "水文与水资源工程", "港口航道与海岸工程", "水务工程", "智慧水利", "水文学及水资源", "水力学及河流动力学", "水工结构工程", "港口、海岸及近海工程", "农田水土工程");
        
        addSynonyms("测绘科学与技术", "测绘工程", "遥感科学与技术", "导航工程", "地理国情监测", "地理空间信息工程", "大地测量学与测量工程", "摄影测量与遥感", "地图制图学与地理信息工程");
        
        addSynonyms("化学工程与技术", "化学工程与工艺", "制药工程", "资源循环科学与工程", "能源化学工程", "化学工程与工业生物工程", "精细化工", "海洋油气工程", "化学工程", "化学工艺", "生物化工", "应用化学", "工业催化");
        
        addSynonyms("交通运输工程", "交通运输", "交通工程", "航海技术", "轮机工程", "飞行技术", "交通设备与控制工程", "救助与打捞工程", "船舶电子电气工程", "轨道交通电气与控制", "邮轮工程与管理", "道路与铁道工程", "交通信息工程及控制", "交通运输规划与管理", "载运工具运用工程", "轨道交通运输", "道路交通运输", "水路交通运输", "航空交通运输", "管道交通运输");
    }
    
    /**
     * 初始化医学门类同义词
     */
    private void initializeMedicalSynonyms() {
        addSynonyms("基础医学", "基础医学", "人体解剖与组织胚胎学", "免疫学", "病原生物学", "病理学与病理生理学", "法医学", "放射医学");
        
        addSynonyms("临床医学", "临床医学", "麻醉学", "医学影像学", "眼视光医学", "精神医学", "放射医学", "儿科学", "内科学", "老年医学", "神经病学", "精神病与精神卫生学", "皮肤病与性病学", "影像医学与核医学", "临床检验诊断学", "外科学", "妇产科学", "眼科学", "耳鼻咽喉科学", "肿瘤学", "康复医学与理疗学", "运动医学", "急诊医学");
        
        addSynonyms("口腔医学", "口腔医学", "口腔基础医学", "口腔临床医学");
        
        addSynonyms("公共卫生与预防医学", "预防医学", "食品卫生与营养学", "妇幼保健医学", "卫生监督", "全球健康学", "流行病与卫生统计学", "劳动卫生与环境卫生学", "营养与食品卫生学", "儿少卫生与妇幼保健学", "卫生毒理学", "军事预防医学", "公共卫生");
        
        addSynonyms("中医学", "中医学", "针灸推拿学", "藏医学", "蒙医学", "维医学", "壮医学", "哈医学", "傣医学", "回医学", "中医康复学", "中医养生学", "中医儿科学", "中医骨伤科学", "中医基础理论", "中医临床基础", "中医医史文献", "方剂学", "中医诊断学", "中医内科学", "中医外科学", "中医妇科学", "中医五官科学", "民族医学", "中医");
        
        addSynonyms("中西医结合", "中西医临床医学", "中西医结合", "中西医结合基础", "中西医结合临床");
        
        addSynonyms("药学", "药学", "药物制剂", "临床药学", "药事管理", "药物分析", "药物化学", "海洋药学", "化妆品科学与技术", "药剂学", "生药学", "药物分析学", "微生物与生化药学", "药理学");
        
        addSynonyms("中药学", "中药学", "中药资源与开发", "藏药学", "蒙药学", "中药制药", "中草药栽培与鉴定", "中药");
        
        addSynonyms("医学技术", "医学检验技术", "医学实验技术", "医学影像技术", "眼视光学", "康复治疗学", "口腔医学技术", "卫生检验与检疫", "听力与言语康复学", "康复物理治疗", "康复作业治疗", "智能医学工程", "生物医药数据科学");
        
        addSynonyms("护理学", "护理学", "助产学", "护理");
    }
    
    /**
     * 初始化管理学门类同义词
     */
    private void initializeManagementSynonyms() {
        addSynonyms("管理科学与工程", "管理科学", "信息管理与信息系统", "工程管理", "房地产开发与管理", "工程造价", "保密管理", "邮政管理", "大数据管理与应用", "工程审计", "计算金融", "项目管理", "工业工程与管理", "物流工程与管理");
        
        addSynonyms("工商管理", "工商管理", "市场营销", "会计学", "财务管理", "国际商务", "人力资源管理", "审计学", "资产评估", "物业管理", "文化产业管理", "劳动关系", "体育经济与管理", "财务会计教育", "市场营销教育", "零售业管理", "特许经营管理", "商务智能", "企业管理", "旅游管理", "技术经济及管理", "会计");
        
        addSynonyms("农业经济管理", "农林经济管理", "农村区域发展", "农业经济管理", "林业经济管理", "农业管理", "农村发展");
        
        addSynonyms("公共管理", "公共事业管理", "行政管理", "劳动与社会保障", "土地资源管理", "城市管理", "海关管理", "交通管理", "海事管理", "公共关系学", "健康服务与管理", "海警后勤管理", "应急管理", "医疗产品管理", "医疗保险", "社会医学与卫生事业管理", "教育经济与管理", "社会保障");
        
        addSynonyms("图书情报与档案管理", "图书馆学", "档案学", "信息资源管理", "情报学", "图书情报");
        
        addSynonyms("物流管理与工程", "物流管理", "物流工程", "采购管理");
        
        addSynonyms("工业工程", "工业工程", "标准化工程", "质量管理工程");
        
        addSynonyms("电子商务", "电子商务", "电子商务及法律");
    }
    
    /**
     * 初始化艺术学门类同义词
     */
    private void initializeArtsSynonyms() {
        addSynonyms("艺术学理论", "艺术史论", "艺术学");
        
        addSynonyms("音乐与舞蹈学", "音乐表演", "音乐学", "作曲与作曲技术理论", "舞蹈表演", "舞蹈学", "舞蹈编导", "舞蹈教育", "航空服务艺术与管理", "流行音乐", "音乐治疗", "音乐", "舞蹈");
        
        addSynonyms("戏剧与影视学", "表演", "戏剧学", "电影学", "戏剧影视文学", "广播电视编导", "戏剧影视导演", "戏剧影视美术设计", "录音艺术", "播音与主持艺术", "动画", "影视摄影与制作", "影视技术", "戏剧教育", "戏剧与影视", "戏曲与曲艺");
        
        addSynonyms("美术学", "美术学", "绘画", "雕塑", "摄影", "书法学", "中国画", "实验艺术", "跨媒体艺术", "文物保护与修复", "漫画", "美术与书法");
        
        addSynonyms("设计学", "艺术设计学", "视觉传达设计", "环境设计", "产品设计", "服装与服饰设计", "公共艺术", "工艺美术", "数字媒体艺术", "艺术与科技", "陶瓷艺术设计", "新媒体艺术", "包装设计", "设计");
    }
    
    /**
     * 初始化新兴交叉专业同义词
     */
    private void initializeEmergingSynonyms() {
        // === 人工智能相关 ===
        addSynonyms("人工智能", "人工智能", "智能科学与技术", "机器人工程", "智能制造工程", "智能车辆工程", "智能建造", "智能医学工程", "智能飞行器技术", "智能无人系统技术", "智能装备与系统", "智能材料与结构", "智能电网信息工程", "智能体育工程", "智能制造技术");
        
        // === 大数据相关 ===
        addSynonyms("大数据", "数据科学与大数据技术", "大数据管理与应用", "大数据技术与工程", "生物医药数据科学", "数据计算及应用");
        
        // === 网络安全相关 ===
        addSynonyms("网络安全", "信息安全", "网络空间安全", "网络安全与执法", "保密技术", "网络与信息安全", "密码");
        
        // === 新能源相关 ===
        addSynonyms("新能源", "新能源科学与工程", "新能源材料与器件", "新能源汽车工程", "储能科学与工程", "清洁能源技术", "储能技术");
        
        // === 生物技术相关 ===
        addSynonyms("生物技术", "生物技术", "生物工程", "生物制药", "生物医学工程", "生物信息学", "生物化工", "生物技术与工程");
        
        // === 集成电路相关 ===
        addSynonyms("集成电路", "集成电路科学与工程", "集成电路设计与集成系统", "集成电路工程", "微电子科学与工程", "微电子学与固体电子学");
        
        // === 数字技术相关 ===
        addSynonyms("数字技术", "数字媒体技术", "数字媒体艺术", "数字出版", "新媒体技术", "新媒体艺术", "数字经济", "虚拟现实技术", "区块链工程");
        
        // === 遥感技术相关 ===
        addSynonyms("遥感技术", "遥感科学与技术", "摄影测量与遥感", "地理信息科学", "地理空间信息工程", "空间信息与数字技术");
        
        // === 纳米技术相关 ===
        addSynonyms("纳米技术", "纳米科学与工程", "纳米材料与技术", "微机电系统工程");
        
        // === 农学相关 ===
        addSynonyms("植物生产", "农学", "园艺", "植物保护", "植物科学与技术", "种子科学与工程", "设施农业科学与工程", "茶学", "作物学", "园艺学", "农艺与种业", "资源利用与植物保护");
        
        addSynonyms("动物科学", "动物科学", "动物医学", "畜牧学", "兽医学", "畜牧", "兽医");
        
        addSynonyms("林学", "林学", "园林", "森林工程", "木材科学与工程", "林产化工", "林业", "风景园林");
        
        addSynonyms("水产", "水产养殖学", "海洋渔业科学与技术", "水族科学与技术", "水生动物医学", "渔业发展");
        
        // === 环境科学相关 ===
        addSynonyms("环境科学", "环境科学与工程", "环境工程", "环境科学", "环境生态工程", "环保设备工程", "资源环境科学", "水质科学与技术");
        
        // === 食品科学相关 ===
        addSynonyms("食品科学", "食品科学与工程", "食品质量与安全", "粮食工程", "乳品工程", "酿酒工程", "葡萄与葡萄酒工程", "食品营养与检验教育", "烹饪与营养教育", "食品卫生与营养学", "农产品加工及贮藏工程", "水产品加工及贮藏工程", "食品加工与安全", "食品工程", "发酵工程", "食品与营养");
    }
    
    /**
     * 添加同义词组
     */
    private void addSynonyms(String key, String... synonyms) {
        Set<String> synonymSet = new HashSet<>(Arrays.asList(synonyms));
        synonymSet.add(key);
        synonymDict.put(key, synonymSet);
    }
    
    /**
     * 初始化跨学科映射关系
     */
    private void initializeCrossDisciplineMap() {
        // 工学与理学的交叉
        addCrossDiscipline("08", "07"); // 工学 <-> 理学
        addCrossDiscipline("07", "08");
        
        // 经济学与管理学的交叉
        addCrossDiscipline("02", "12"); // 经济学 <-> 管理学
        addCrossDiscipline("12", "02");
        
        // 文学与教育学的交叉
        addCrossDiscipline("05", "04"); // 文学 <-> 教育学
        addCrossDiscipline("04", "05");
        
        // 法学与管理学的交叉
        addCrossDiscipline("03", "12"); // 法学 <-> 管理学
        addCrossDiscipline("12", "03");
        
        // 医学与理学的交叉
        addCrossDiscipline("10", "07"); // 医学 <-> 理学
        addCrossDiscipline("07", "10");
        
        // 农学与工学的交叉
        addCrossDiscipline("09", "08"); // 农学 <-> 工学
        addCrossDiscipline("08", "09");
    }
    
    /**
     * 添加跨学科映射关系
     */
    private void addCrossDiscipline(String category1, String category2) {
        crossDisciplineMap.computeIfAbsent(category1, k -> new HashSet<>()).add(category2);
    }
    
    // Getters
    public List<UndergraduateMajor> getUndergraduateMajors() {
        return undergraduateMajors;
    }
    
    public List<GraduateMajor> getGraduateMajors() {
        return graduateMajors;
    }
}