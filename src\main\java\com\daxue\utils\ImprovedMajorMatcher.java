package com.daxue.utils;

import com.daxue.db.UndergraduateMajor;
import com.daxue.db.GraduateMajor;
import com.daxue.db.MatchResult;
import com.daxue.db.MatchResult.MatchType;

import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 改进的专业匹配算法工具类
 * 解决原版本的性能和设计问题
 * 
 * 主要改进：
 * 1. 预计算和缓存机制
 * 2. 优化的同义词查找
 * 3. 可配置的匹配参数
 * 4. 更好的错误处理
 * 5. 性能监控
 */
public class ImprovedMajorMatcher {
    
    /** 本科专业数据 */
    private final List<UndergraduateMajor> undergraduateMajors;
    
    /** 研究生专业数据 */
    private final List<GraduateMajor> graduateMajors;
    
    /** 预计算的本科专业关键词缓存 */
    private final Map<String, Set<String>> undergraduateKeywordsCache;
    
    /** 预计算的研究生专业关键词缓存 */
    private final Map<String, Set<String>> graduateKeywordsCache;
    
    /** 优化的同义词查找映射 */
    private final Map<String, Set<String>> synonymLookup;
    
    /** 跨学科映射关系 */
    private final Map<String, Set<String>> crossDisciplineMap;
    
    /** 匹配配置 */
    private final MatchConfig config;
    
    /** 性能统计 */
    private final MatchStatistics statistics;
    
    /**
     * 匹配配置类
     */
    public static class MatchConfig {
        public double exactMatchWeight = 0.4;
        public double keywordMatchWeight = 0.25;
        public double synonymMatchWeight = 0.2;
        public double categoryMatchWeight = 0.1;
        public double crossDisciplineWeight = 0.05;
        
        public double keywordSimilarityThreshold = 0.3;
        public double minMatchScore = 5.0;
        public int maxResults = 10;
        
        // 构造器和getter/setter方法
        public MatchConfig() {}
        
        public MatchConfig setExactMatchWeight(double weight) {
            this.exactMatchWeight = weight;
            return this;
        }
        
        public MatchConfig setKeywordMatchWeight(double weight) {
            this.keywordMatchWeight = weight;
            return this;
        }
        
        public MatchConfig setSynonymMatchWeight(double weight) {
            this.synonymMatchWeight = weight;
            return this;
        }
        
        public MatchConfig setCategoryMatchWeight(double weight) {
            this.categoryMatchWeight = weight;
            return this;
        }
        
        public MatchConfig setCrossDisciplineWeight(double weight) {
            this.crossDisciplineWeight = weight;
            return this;
        }
        
        public MatchConfig setKeywordSimilarityThreshold(double threshold) {
            this.keywordSimilarityThreshold = threshold;
            return this;
        }
        
        public MatchConfig setMinMatchScore(double score) {
            this.minMatchScore = score;
            return this;
        }
        
        public MatchConfig setMaxResults(int maxResults) {
            this.maxResults = maxResults;
            return this;
        }
    }
    
    /**
     * 性能统计类
     */
    public static class MatchStatistics {
        private long totalMatches = 0;
        private long totalTime = 0;
        private final Map<MatchType, Long> matchTypeCount = new ConcurrentHashMap<>();
        
        public void recordMatch(MatchType type, long timeMs) {
            totalMatches++;
            totalTime += timeMs;
            matchTypeCount.merge(type, 1L, Long::sum);
        }
        
        public double getAverageTime() {
            return totalMatches > 0 ? (double) totalTime / totalMatches : 0;
        }
        
        public long getTotalMatches() { return totalMatches; }
        public long getTotalTime() { return totalTime; }
        public Map<MatchType, Long> getMatchTypeCount() { return new HashMap<>(matchTypeCount); }
        
        public void reset() {
            totalMatches = 0;
            totalTime = 0;
            matchTypeCount.clear();
        }
        
        @Override
        public String toString() {
            return String.format("MatchStatistics{totalMatches=%d, avgTime=%.2fms, typeCount=%s}", 
                totalMatches, getAverageTime(), matchTypeCount);
        }
    }
    
    /**
     * 构造器
     */
    public ImprovedMajorMatcher(List<UndergraduateMajor> undergraduateMajors, 
                               List<GraduateMajor> graduateMajors) {
        this(undergraduateMajors, graduateMajors, new MatchConfig());
    }
    
    /**
     * 带配置的构造器
     */
    public ImprovedMajorMatcher(List<UndergraduateMajor> undergraduateMajors, 
                               List<GraduateMajor> graduateMajors, 
                               MatchConfig config) {
        this.undergraduateMajors = undergraduateMajors != null ? undergraduateMajors : new ArrayList<>();
        this.graduateMajors = graduateMajors != null ? graduateMajors : new ArrayList<>();
        this.config = config != null ? config : new MatchConfig();
        this.statistics = new MatchStatistics();
        
        // 预计算缓存
        this.undergraduateKeywordsCache = new ConcurrentHashMap<>();
        this.graduateKeywordsCache = new ConcurrentHashMap<>();
        this.synonymLookup = new ConcurrentHashMap<>();
        this.crossDisciplineMap = new ConcurrentHashMap<>();
        
        // 初始化
        initializeCaches();
        initializeSynonymLookup();
        initializeCrossDisciplineMap();
    }
    
    /**
     * 主匹配方法 - 改进版本
     */
    public List<MatchResult> findMatchingGraduateMajors(String undergraduateMajorName) {
        long startTime = System.currentTimeMillis();
        
        try {
            if (undergraduateMajorName == null || undergraduateMajorName.trim().isEmpty()) {
                return new ArrayList<>();
            }
            
            List<MatchResult> allResults = new ArrayList<>();
            
            // 1. 精确匹配
            allResults.addAll(exactMatch(undergraduateMajorName));
            
            // 2. 同义词匹配（优先级高于关键词匹配）
            allResults.addAll(synonymMatch(undergraduateMajorName));
            
            // 3. 关键词匹配
            allResults.addAll(keywordMatch(undergraduateMajorName));
            
            // 4. 学科门类匹配
            allResults.addAll(categoryMatch(undergraduateMajorName));
            
            // 5. 交叉学科匹配
            allResults.addAll(crossDisciplineMatch(undergraduateMajorName));
            
            // 去重、过滤和排序
            List<MatchResult> finalResults = deduplicateAndSort(allResults);
            
            // 记录统计信息
            long endTime = System.currentTimeMillis();
            for (MatchResult result : finalResults) {
                statistics.recordMatch(result.getMatchType(), endTime - startTime);
            }
            
            return finalResults;
            
        } catch (Exception e) {
            // 错误处理
            System.err.println("匹配过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
     * 初始化缓存
     */
    private void initializeCaches() {
        // 预计算本科专业关键词
        for (UndergraduateMajor major : undergraduateMajors) {
            if (major.getMajorName() != null) {
                Set<String> keywords = extractKeywords(major.getMajorName());
                undergraduateKeywordsCache.put(major.getMajorName(), keywords);
            }
        }
        
        // 预计算研究生专业关键词
        for (GraduateMajor major : graduateMajors) {
            if (major.getMajorName() != null) {
                Set<String> keywords = extractKeywords(major.getMajorName());
                graduateKeywordsCache.put(major.getMajorName(), keywords);
            }
        }
    }
    
    /**
     * 提取关键词的改进方法
     */
    private Set<String> extractKeywords(String majorName) {
        if (majorName == null || majorName.trim().isEmpty()) {
            return new HashSet<>();
        }
        
        Set<String> keywords = new HashSet<>();
        
        // 清理专业名称
        String cleanName = majorName.replaceAll("[()（）\\[\\]【】<>《》、，。；：！？\\s]+", "");
        
        // 定义核心学科词汇模式（改进版）
        String[] patterns = {
            "计算机", "软件", "网络", "信息", "数据", "人工智能", "大数据", "物联网", "区块链",
            "电子", "通信", "自动化", "电气", "机械", "材料", "化学", "土木", "建筑",
            "经济", "金融", "管理", "会计", "市场", "法学", "教育", "心理", "医学",
            "生物", "环境", "能源", "交通", "航空", "海洋", "地理", "数学", "物理"
        };
        
        // 提取匹配的关键词
        for (String pattern : patterns) {
            if (cleanName.contains(pattern)) {
                keywords.add(pattern);
            }
        }
        
        // 如果没有匹配到预定义模式，使用简单分词
        if (keywords.isEmpty() && cleanName.length() > 0) {
            // 简单的中文分词逻辑
            for (int i = 0; i < cleanName.length() - 1; i++) {
                String segment = cleanName.substring(i, Math.min(i + 3, cleanName.length()));
                if (segment.length() >= 2) {
                    keywords.add(segment);
                }
            }
        }
        
        return keywords;
    }

    /**
     * 精确匹配 - 改进版本
     */
    private List<MatchResult> exactMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();

        for (GraduateMajor grad : graduateMajors) {
            if (majorName.equals(grad.getMajorName())) {
                double score = 100.0 * config.exactMatchWeight;
                results.add(new MatchResult(grad, score, MatchType.EXACT_MATCH,
                    "专业名称完全匹配"));
            }
        }

        return results;
    }

    /**
     * 关键词匹配 - 改进版本（使用预计算缓存）
     */
    private List<MatchResult> keywordMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();

        // 从缓存获取本科专业关键词
        Set<String> undergraduateKeywords = undergraduateKeywordsCache.get(majorName);
        if (undergraduateKeywords == null || undergraduateKeywords.isEmpty()) {
            // 如果缓存中没有，实时计算
            undergraduateKeywords = extractKeywords(majorName);
        }

        if (undergraduateKeywords.isEmpty()) {
            return results;
        }

        for (GraduateMajor grad : graduateMajors) {
            // 从缓存获取研究生专业关键词
            Set<String> graduateKeywords = graduateKeywordsCache.get(grad.getMajorName());
            if (graduateKeywords == null) {
                graduateKeywords = extractKeywords(grad.getMajorName());
                graduateKeywordsCache.put(grad.getMajorName(), graduateKeywords);
            }

            if (!graduateKeywords.isEmpty()) {
                double similarity = calculateJaccardSimilarity(undergraduateKeywords, graduateKeywords);
                if (similarity > config.keywordSimilarityThreshold) {
                    double score = similarity * 100.0 * config.keywordMatchWeight;
                    results.add(new MatchResult(grad, score, MatchType.KEYWORD_MATCH,
                        String.format("关键词相似度: %.2f", similarity)));
                }
            }
        }

        return results;
    }

    /**
     * 同义词匹配 - 改进版本（使用优化的查找）
     */
    private List<MatchResult> synonymMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();

        Set<String> synonyms = synonymLookup.get(majorName);
        if (synonyms != null) {
            for (GraduateMajor grad : graduateMajors) {
                if (synonyms.contains(grad.getMajorName())) {
                    double score = 90.0 * config.synonymMatchWeight;
                    results.add(new MatchResult(grad, score, MatchType.SYNONYM_MATCH,
                        "基于同义词词典匹配"));
                }
            }
        }

        return results;
    }

    /**
     * 学科门类匹配 - 改进版本
     */
    private List<MatchResult> categoryMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();

        // 找到本科专业的学科门类
        String categoryCode = findCategoryCodeByMajorName(majorName);
        if (categoryCode == null) {
            return results;
        }

        for (GraduateMajor grad : graduateMajors) {
            if (categoryCode.equals(grad.getCategoryOneCode())) {
                double score = 60.0 * config.categoryMatchWeight;
                results.add(new MatchResult(grad, score, MatchType.CATEGORY_MATCH,
                    "同属" + grad.getCategoryOne() + "学科门类"));
            }
        }

        return results;
    }

    /**
     * 交叉学科匹配 - 改进版本
     */
    private List<MatchResult> crossDisciplineMatch(String majorName) {
        List<MatchResult> results = new ArrayList<>();

        String categoryCode = findCategoryCodeByMajorName(majorName);
        if (categoryCode == null) {
            return results;
        }

        Set<String> relatedCategories = crossDisciplineMap.get(categoryCode);
        if (relatedCategories != null) {
            for (GraduateMajor grad : graduateMajors) {
                if (relatedCategories.contains(grad.getCategoryOneCode())) {
                    double score = 40.0 * config.crossDisciplineWeight;
                    results.add(new MatchResult(grad, score, MatchType.CROSS_DISCIPLINE_MATCH,
                        "跨学科相关专业"));
                }
            }
        }

        return results;
    }

    /**
     * 计算Jaccard相似度 - 改进版本
     */
    private double calculateJaccardSimilarity(Set<String> set1, Set<String> set2) {
        if (set1 == null || set2 == null || set1.isEmpty() || set2.isEmpty()) {
            return 0.0;
        }

        // 使用更高效的集合操作
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);

        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 根据专业名称查找学科门类代码 - 改进版本
     */
    private String findCategoryCodeByMajorName(String majorName) {
        // 使用Stream API提高效率
        return undergraduateMajors.stream()
            .filter(major -> majorName.equals(major.getMajorName()))
            .map(UndergraduateMajor::getCategoryOneCode)
            .findFirst()
            .orElse(null);
    }

    /**
     * 去重并按分数排序 - 改进版本
     */
    private List<MatchResult> deduplicateAndSort(List<MatchResult> results) {
        // 按专业代码去重，保留分数最高的
        Map<String, MatchResult> uniqueResults = new HashMap<>();

        for (MatchResult result : results) {
            String majorCode = result.getGraduateMajor().getMajorCode();
            if (!uniqueResults.containsKey(majorCode) ||
                uniqueResults.get(majorCode).getMatchScore() < result.getMatchScore()) {
                uniqueResults.put(majorCode, result);
            }
        }

        // 按分数降序排序，过滤低分结果
        return uniqueResults.values().stream()
                .filter(result -> result.getMatchScore() >= config.minMatchScore)
                .sorted((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()))
                .limit(config.maxResults)
                .collect(Collectors.toList());
    }

    /**
     * 初始化优化的同义词查找映射
     */
    private void initializeSynonymLookup() {
        // 创建基础同义词组
        Map<String, Set<String>> synonymGroups = createBasicSynonymGroups();

        // 构建快速查找映射
        for (Map.Entry<String, Set<String>> entry : synonymGroups.entrySet()) {
            Set<String> synonyms = entry.getValue();
            for (String synonym : synonyms) {
                synonymLookup.put(synonym, new HashSet<>(synonyms));
            }
        }
    }

    /**
     * 创建基础同义词组（简化版本）
     */
    private Map<String, Set<String>> createBasicSynonymGroups() {
        Map<String, Set<String>> groups = new HashMap<>();

        // 计算机相关
        groups.put("计算机", new HashSet<>(Arrays.asList(
            "计算机科学与技术", "软件工程", "网络工程", "信息安全", "物联网工程",
            "数字媒体技术", "智能科学与技术", "数据科学与大数据技术")));

        // 电子信息相关
        groups.put("电子信息", new HashSet<>(Arrays.asList(
            "电子信息工程", "电子科学与技术", "通信工程", "微电子科学与工程",
            "光电信息科学与工程", "信息工程")));

        // 机械相关
        groups.put("机械", new HashSet<>(Arrays.asList(
            "机械工程", "机械设计制造及其自动化", "材料成型及控制工程",
            "机械电子工程", "车辆工程")));

        // 经济管理相关
        groups.put("经济管理", new HashSet<>(Arrays.asList(
            "经济学", "金融学", "国际经济与贸易", "工商管理", "市场营销",
            "会计学", "财务管理", "人力资源管理")));

        return groups;
    }

    /**
     * 初始化跨学科映射关系
     */
    private void initializeCrossDisciplineMap() {
        // 工学与理学的交叉
        addCrossDiscipline("08", "07"); // 工学 <-> 理学
        addCrossDiscipline("07", "08");

        // 经济学与管理学的交叉
        addCrossDiscipline("02", "12"); // 经济学 <-> 管理学
        addCrossDiscipline("12", "02");

        // 文学与教育学的交叉
        addCrossDiscipline("05", "04"); // 文学 <-> 教育学
        addCrossDiscipline("04", "05");

        // 法学与管理学的交叉
        addCrossDiscipline("03", "12"); // 法学 <-> 管理学
        addCrossDiscipline("12", "03");

        // 医学与理学的交叉
        addCrossDiscipline("10", "07"); // 医学 <-> 理学
        addCrossDiscipline("07", "10");

        // 农学与工学的交叉
        addCrossDiscipline("09", "08"); // 农学 <-> 工学
        addCrossDiscipline("08", "09");
    }

    /**
     * 添加跨学科映射关系
     */
    private void addCrossDiscipline(String category1, String category2) {
        crossDisciplineMap.computeIfAbsent(category1, k -> new HashSet<>()).add(category2);
    }

    // Getter方法
    public MatchConfig getConfig() { return config; }
    public MatchStatistics getStatistics() { return statistics; }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        statistics.reset();
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheInfo() {
        return String.format("缓存信息: 本科专业关键词=%d, 研究生专业关键词=%d, 同义词映射=%d",
            undergraduateKeywordsCache.size(),
            graduateKeywordsCache.size(),
            synonymLookup.size());
    }
}
