package com.daxue.utils;

import com.daxue.db.MatchResult;
import com.daxue.db.GraduateMajor;
import com.daxue.db.MatchResult.MatchType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * deduplicateAndSort 方法详细分析和改进建议
 * 
 * 原方法存在的问题：
 * 1. 硬编码的结果数量限制 (limit(10))
 * 2. 没有最低分数阈值过滤
 * 3. 可能返回低质量匹配结果
 * 4. 缺少性能优化
 */
public class DeduplicateAndSortAnalysis {
    
    /**
     * 原版本的 deduplicateAndSort 方法分析
     */
    public static class OriginalAnalysis {
        
        /**
         * 原方法的问题分析
         */
        public static void analyzeProblems() {
            System.out.println("🔍 原版 deduplicateAndSort 方法问题分析:");
            System.out.println("=" .repeat(60));
            
            System.out.println("❌ 问题1: 硬编码结果数量限制");
            System.out.println("   - .limit(10) 硬编码，无法动态调整");
            System.out.println("   - 不同场景可能需要不同的结果数量");
            System.out.println();
            
            System.out.println("❌ 问题2: 缺少质量过滤");
            System.out.println("   - 没有最低分数阈值");
            System.out.println("   - 可能返回分数很低的无意义匹配");
            System.out.println("   - 例如：分数只有1-2分的匹配结果");
            System.out.println();
            
            System.out.println("❌ 问题3: 去重逻辑可能有问题");
            System.out.println("   - 只按专业代码去重");
            System.out.println("   - 如果专业代码为null会出现问题");
            System.out.println("   - 没有考虑专业名称相同但代码不同的情况");
            System.out.println();
            
            System.out.println("❌ 问题4: 性能问题");
            System.out.println("   - 每次都要遍历所有结果进行去重");
            System.out.println("   - Stream操作可能不是最优选择");
            System.out.println("   - 没有早期过滤低分结果");
            System.out.println();
        }
        
        /**
         * 原方法的时间复杂度分析
         */
        public static void analyzeComplexity() {
            System.out.println("⏱️  时间复杂度分析:");
            System.out.println("-".repeat(40));
            System.out.println("去重阶段: O(n) - 遍历所有结果");
            System.out.println("排序阶段: O(m log m) - 其中m是去重后的结果数");
            System.out.println("限制阶段: O(1) - 只取前10个");
            System.out.println("总体复杂度: O(n + m log m)");
            System.out.println();
            System.out.println("💾 空间复杂度: O(m) - 存储去重后的结果");
            System.out.println();
        }
    }
    
    /**
     * 改进版本的 deduplicateAndSort 方法
     */
    public static class ImprovedVersion {
        
        /**
         * 改进版本1: 添加配置化和质量过滤
         */
        public static List<MatchResult> deduplicateAndSortV1(List<MatchResult> results, 
                                                           double minScore, int maxResults) {
            if (results == null || results.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 按专业代码去重，保留分数最高的
            Map<String, MatchResult> uniqueResults = new HashMap<>();
            
            for (MatchResult result : results) {
                // 添加空值检查
                if (result == null || result.getGraduateMajor() == null) {
                    continue;
                }
                
                String majorCode = result.getGraduateMajor().getMajorCode();
                if (majorCode == null || majorCode.trim().isEmpty()) {
                    continue; // 跳过无效的专业代码
                }
                
                // 早期过滤低分结果
                if (result.getMatchScore() < minScore) {
                    continue;
                }
                
                if (!uniqueResults.containsKey(majorCode) || 
                    uniqueResults.get(majorCode).getMatchScore() < result.getMatchScore()) {
                    uniqueResults.put(majorCode, result);
                }
            }
            
            // 按分数降序排序并限制结果数量
            return uniqueResults.values().stream()
                    .sorted((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()))
                    .limit(maxResults)
                    .collect(Collectors.toList());
        }
        
        /**
         * 改进版本2: 性能优化版本
         */
        public static List<MatchResult> deduplicateAndSortV2(List<MatchResult> results, 
                                                           double minScore, int maxResults) {
            if (results == null || results.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 使用 TreeMap 自动排序，key为负分数以实现降序
            Map<String, MatchResult> uniqueResults = new HashMap<>();
            
            for (MatchResult result : results) {
                // 验证结果有效性
                if (!isValidResult(result, minScore)) {
                    continue;
                }
                
                String majorCode = result.getGraduateMajor().getMajorCode();
                
                // 只保留分数更高的结果
                MatchResult existing = uniqueResults.get(majorCode);
                if (existing == null || existing.getMatchScore() < result.getMatchScore()) {
                    uniqueResults.put(majorCode, result);
                }
            }
            
            // 如果结果数量不多，直接排序
            if (uniqueResults.size() <= maxResults * 2) {
                return uniqueResults.values().stream()
                        .sorted((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()))
                        .limit(maxResults)
                        .collect(Collectors.toList());
            }
            
            // 对于大量结果，使用优先队列优化
            PriorityQueue<MatchResult> topResults = new PriorityQueue<>(
                maxResults, Comparator.comparingDouble(MatchResult::getMatchScore));
            
            for (MatchResult result : uniqueResults.values()) {
                if (topResults.size() < maxResults) {
                    topResults.offer(result);
                } else if (result.getMatchScore() > topResults.peek().getMatchScore()) {
                    topResults.poll();
                    topResults.offer(result);
                }
            }
            
            // 转换为降序列表
            List<MatchResult> finalResults = new ArrayList<>(topResults);
            finalResults.sort((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()));
            
            return finalResults;
        }
        
        /**
         * 改进版本3: 智能去重版本
         */
        public static List<MatchResult> deduplicateAndSortV3(List<MatchResult> results, 
                                                           double minScore, int maxResults) {
            if (results == null || results.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 多级去重：先按专业代码，再按专业名称
            Map<String, MatchResult> codeBasedResults = new HashMap<>();
            Map<String, MatchResult> nameBasedResults = new HashMap<>();
            
            for (MatchResult result : results) {
                if (!isValidResult(result, minScore)) {
                    continue;
                }
                
                String majorCode = result.getGraduateMajor().getMajorCode();
                String majorName = result.getGraduateMajor().getMajorName();
                
                // 按专业代码去重
                if (majorCode != null && !majorCode.trim().isEmpty()) {
                    MatchResult existing = codeBasedResults.get(majorCode);
                    if (existing == null || existing.getMatchScore() < result.getMatchScore()) {
                        codeBasedResults.put(majorCode, result);
                    }
                }
                
                // 按专业名称去重（处理代码缺失的情况）
                if (majorName != null && !majorName.trim().isEmpty()) {
                    MatchResult existing = nameBasedResults.get(majorName);
                    if (existing == null || existing.getMatchScore() < result.getMatchScore()) {
                        nameBasedResults.put(majorName, result);
                    }
                }
            }
            
            // 合并结果，优先使用代码去重的结果
            Set<String> processedNames = new HashSet<>();
            List<MatchResult> finalResults = new ArrayList<>();
            
            // 添加按代码去重的结果
            for (MatchResult result : codeBasedResults.values()) {
                finalResults.add(result);
                processedNames.add(result.getGraduateMajor().getMajorName());
            }
            
            // 添加按名称去重但代码缺失的结果
            for (MatchResult result : nameBasedResults.values()) {
                if (!processedNames.contains(result.getGraduateMajor().getMajorName())) {
                    finalResults.add(result);
                }
            }
            
            // 排序并限制结果数量
            return finalResults.stream()
                    .sorted((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()))
                    .limit(maxResults)
                    .collect(Collectors.toList());
        }
        
        /**
         * 验证匹配结果的有效性
         */
        private static boolean isValidResult(MatchResult result, double minScore) {
            if (result == null || result.getGraduateMajor() == null) {
                return false;
            }
            
            if (result.getMatchScore() < minScore) {
                return false;
            }
            
            GraduateMajor major = result.getGraduateMajor();
            return major.getMajorCode() != null || major.getMajorName() != null;
        }
    }
    
    /**
     * 性能测试比较
     */
    public static void performanceComparison() {
        System.out.println("🚀 deduplicateAndSort 方法性能对比:");
        System.out.println("=" .repeat(60));
        
        // 创建测试数据
        List<MatchResult> testResults = createTestData(1000);
        
        // 测试原版本（模拟）
        long start1 = System.nanoTime();
        List<MatchResult> results1 = ImprovedVersion.deduplicateAndSortV1(testResults, 5.0, 10);
        long time1 = System.nanoTime() - start1;
        
        // 测试改进版本2
        long start2 = System.nanoTime();
        List<MatchResult> results2 = ImprovedVersion.deduplicateAndSortV2(testResults, 5.0, 10);
        long time2 = System.nanoTime() - start2;
        
        // 测试改进版本3
        long start3 = System.nanoTime();
        List<MatchResult> results3 = ImprovedVersion.deduplicateAndSortV3(testResults, 5.0, 10);
        long time3 = System.nanoTime() - start3;
        
        System.out.printf("基础改进版本: %d ns, 结果数: %d\n", time1, results1.size());
        System.out.printf("性能优化版本: %d ns, 结果数: %d\n", time2, results2.size());
        System.out.printf("智能去重版本: %d ns, 结果数: %d\n", time3, results3.size());
        
        double speedup2 = (double) time1 / time2;
        double speedup3 = (double) time1 / time3;
        
        System.out.printf("性能提升: 版本2 %.2fx, 版本3 %.2fx\n", speedup2, speedup3);
    }
    
    /**
     * 创建测试数据
     */
    private static List<MatchResult> createTestData(int count) {
        List<MatchResult> results = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重复性
        
        for (int i = 0; i < count; i++) {
            GraduateMajor major = new GraduateMajor();
            major.setMajorCode("CODE" + (i % 100)); // 模拟重复的专业代码
            major.setMajorName("专业" + i);
            
            double score = random.nextDouble() * 100;
            MatchType type = MatchType.values()[random.nextInt(MatchType.values().length)];
            
            results.add(new MatchResult(major, score, type, "测试匹配"));
        }
        
        return results;
    }
    
    public static void main(String[] args) {
        OriginalAnalysis.analyzeProblems();
        OriginalAnalysis.analyzeComplexity();
        performanceComparison();
    }
}
