package com.daxue.utils;

import com.daxue.db.UndergraduateMajor;
import com.daxue.db.GraduateMajor;
import com.daxue.db.MatchResult;

import java.util.*;

/**
 * 专业匹配器性能对比测试
 * 对比原版本和改进版本的性能差异
 */
public class MajorMatcherPerformanceTest {
    
    public static void main(String[] args) {
        System.out.println("🚀 专业匹配算法性能对比测试");
        System.out.println("=" .repeat(60));
        
        // 获取测试数据
        List<UndergraduateMajor> undergraduateMajors = MajorDataRepository.getUndergraduateMajors();
        List<GraduateMajor> graduateMajors = MajorDataRepository.getGraduateMajors();
        
        System.out.printf("📊 测试数据: 本科专业 %d 个, 研究生专业 %d 个\n", 
            undergraduateMajors.size(), graduateMajors.size());
        System.out.println();
        
        // 测试用例
        String[] testCases = {
            "计算机科学与技术",
            "软件工程", 
            "电子信息工程",
            "机械设计制造及其自动化",
            "金融学",
            "临床医学",
            "土木工程",
            "英语",
            "数学与应用数学",
            "工商管理"
        };
        
        // 创建匹配器实例
        MajorMatcher originalMatcher = new MajorMatcher(undergraduateMajors, graduateMajors);
        
        ImprovedMajorMatcher.MatchConfig config = new ImprovedMajorMatcher.MatchConfig()
            .setKeywordSimilarityThreshold(0.25)  // 降低阈值以获得更多结果
            .setMaxResults(15);
        ImprovedMajorMatcher improvedMatcher = new ImprovedMajorMatcher(
            undergraduateMajors, graduateMajors, config);
        
        System.out.println("🔧 初始化完成，开始性能测试...\n");
        
        // 预热
        warmUp(originalMatcher, improvedMatcher, testCases);
        
        // 性能测试
        performanceTest(originalMatcher, improvedMatcher, testCases);
        
        // 结果质量对比
        qualityComparison(originalMatcher, improvedMatcher, testCases);
        
        // 显示改进版本的统计信息
        System.out.println("\n📈 改进版本统计信息:");
        System.out.println(improvedMatcher.getStatistics());
        System.out.println(improvedMatcher.getCacheInfo());
    }
    
    /**
     * 预热测试
     */
    private static void warmUp(MajorMatcher original, ImprovedMajorMatcher improved, String[] testCases) {
        System.out.println("🔥 预热中...");
        for (int i = 0; i < 3; i++) {
            for (String testCase : testCases) {
                original.findMatchingGraduateMajors(testCase);
                improved.findMatchingGraduateMajors(testCase);
            }
        }
        System.out.println("✅ 预热完成\n");
    }
    
    /**
     * 性能测试
     */
    private static void performanceTest(MajorMatcher original, ImprovedMajorMatcher improved, String[] testCases) {
        System.out.println("⏱️  性能测试结果:");
        System.out.println("-".repeat(80));
        System.out.printf("%-25s | %-12s | %-12s | %-12s | %-8s\n", 
            "专业名称", "原版本(ms)", "改进版本(ms)", "提升倍数", "结果数量");
        System.out.println("-".repeat(80));
        
        long totalOriginalTime = 0;
        long totalImprovedTime = 0;
        
        for (String testCase : testCases) {
            // 测试原版本
            long originalStart = System.nanoTime();
            List<MatchResult> originalResults = original.findMatchingGraduateMajors(testCase);
            long originalTime = (System.nanoTime() - originalStart) / 1_000_000; // 转换为毫秒
            
            // 测试改进版本
            long improvedStart = System.nanoTime();
            List<MatchResult> improvedResults = improved.findMatchingGraduateMajors(testCase);
            long improvedTime = (System.nanoTime() - improvedStart) / 1_000_000; // 转换为毫秒
            
            totalOriginalTime += originalTime;
            totalImprovedTime += improvedTime;
            
            double speedup = improvedTime > 0 ? (double) originalTime / improvedTime : 0;
            
            System.out.printf("%-25s | %-12d | %-12d | %-12.2fx | %-8d\n", 
                truncate(testCase, 24), originalTime, improvedTime, speedup, improvedResults.size());
        }
        
        System.out.println("-".repeat(80));
        double avgSpeedup = totalImprovedTime > 0 ? (double) totalOriginalTime / totalImprovedTime : 0;
        System.out.printf("%-25s | %-12d | %-12d | %-12.2fx | %-8s\n", 
            "平均", totalOriginalTime / testCases.length, totalImprovedTime / testCases.length, 
            avgSpeedup, "N/A");
        System.out.println();
    }
    
    /**
     * 结果质量对比
     */
    private static void qualityComparison(MajorMatcher original, ImprovedMajorMatcher improved, String[] testCases) {
        System.out.println("🎯 匹配质量对比:");
        System.out.println("-".repeat(60));
        
        for (String testCase : testCases) {
            System.out.printf("\n📚 测试专业: %s\n", testCase);
            
            List<MatchResult> originalResults = original.findMatchingGraduateMajors(testCase);
            List<MatchResult> improvedResults = improved.findMatchingGraduateMajors(testCase);
            
            System.out.printf("   原版本结果数: %d, 改进版本结果数: %d\n", 
                originalResults.size(), improvedResults.size());
            
            // 显示前3个结果的对比
            System.out.println("   🏆 Top 3 匹配结果对比:");
            
            int maxResults = Math.max(originalResults.size(), improvedResults.size());
            maxResults = Math.min(maxResults, 3);
            
            for (int i = 0; i < maxResults; i++) {
                System.out.printf("   %d. ", i + 1);
                
                if (i < originalResults.size()) {
                    MatchResult orig = originalResults.get(i);
                    System.out.printf("原版: %s (%.1f) | ", 
                        truncate(orig.getGraduateMajor().getMajorName(), 20), orig.getMatchScore());
                } else {
                    System.out.printf("原版: %-20s (--) | ", "无");
                }
                
                if (i < improvedResults.size()) {
                    MatchResult imp = improvedResults.get(i);
                    System.out.printf("改进: %s (%.1f)", 
                        truncate(imp.getGraduateMajor().getMajorName(), 20), imp.getMatchScore());
                } else {
                    System.out.printf("改进: %-20s (--)", "无");
                }
                
                System.out.println();
            }
        }
    }
    
    /**
     * 截断字符串
     */
    private static String truncate(String str, int maxLength) {
        if (str == null) return "";
        return str.length() <= maxLength ? str : str.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * 内存使用情况测试
     */
    public static void memoryUsageTest() {
        System.out.println("\n💾 内存使用情况测试:");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试前的内存使用
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建匹配器
        List<UndergraduateMajor> undergraduateMajors = MajorDataRepository.getUndergraduateMajors();
        List<GraduateMajor> graduateMajors = MajorDataRepository.getGraduateMajors();
        
        MajorMatcher originalMatcher = new MajorMatcher(undergraduateMajors, graduateMajors);
        ImprovedMajorMatcher improvedMatcher = new ImprovedMajorMatcher(undergraduateMajors, graduateMajors);
        
        // 测试后的内存使用
        System.gc();
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsed = afterMemory - beforeMemory;
        System.out.printf("创建匹配器使用内存: %.2f MB\n", memoryUsed / (1024.0 * 1024.0));
        System.out.println(improvedMatcher.getCacheInfo());
    }
}
